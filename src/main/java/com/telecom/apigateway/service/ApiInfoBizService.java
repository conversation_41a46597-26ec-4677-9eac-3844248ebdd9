package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.setting.yaml.YamlUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.ApiImportFromFileDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.ApiDecrypt;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiInfoTree;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.LogEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.request.QueryLogRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiRequest;
import com.telecom.apigateway.model.vo.response.ApiAttackStatResponse;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.model.vo.response.ApiRiskSummaryResponse;
import com.telecom.apigateway.model.vo.response.ApiStatResponse;
import com.telecom.apigateway.model.vo.response.ApiSummaryResponse;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.LogQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import com.telecom.apigateway.utils.DateTimeUtils;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.SwaggerParserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-08-13
 */
@Slf4j
@Service
public class ApiInfoBizService {

    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private RiskLogService riskLogService;
    @Resource
    private ApiDecryptService apiDecryptService;
    @Resource
    private LogBizService logBizService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;
    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private RiskLogNewService riskLogNewService;

    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.API_INFO,
            displayName = "@apiInfoService.getById(#apiId).name"
    )
    public void deleteByApiId(String apiId) {
        ApiInfo apiInfo = apiInfoService.getById(apiId);

        log.info("开始删除 API ID: {}", apiId);
        List<DeleteOperation> deleteOperations = Arrays.asList(
                () -> apiInfoService.deleteById(apiId),
                () -> sensitiveApiService.deleteByApiId(apiId),
                () -> sensitiveWhiteListService.deleteByApiId(apiId),
                () -> apiDecryptService.deleteByApiId(apiId),
                // todo 优化, 日志过多时删除慢
                () -> nginxAccessLogService.deleteByApiId(apiId),
                // () -> nginxAccessLogService.deleteByApi(apiInfo),
                () -> blocklistService.deleteByApiId(apiId),
                () -> abrService.deleteByApiId(apiId),
                () -> abrtService.deleteByApiId(apiId),
                () -> apiDecryptService.deleteByApiId(apiId),
                () -> sensitiveLogService.deleteByApiId(apiId),
                () -> riskLogNewService.deleteByApiId(apiId)
        );

        // 执行删除操作
        try {
            deleteOperations.forEach(operation -> {
                operation.delete();
                log.debug("成功执行删除api操作: apiId:{}", apiId);
            });
        } catch (Exception e) {
            log.error("删除api失败: {}", apiId, e);
            throw new BusinessException(ResultCodeEnum.DELETE_API_ERROR);
        }

        log.info("API ID: {} 删除完成", apiId);
    }

    // 定义删除操作接口
    @FunctionalInterface
    private interface DeleteOperation {
        void delete();
    }

    public List<ApiQueryResponse> query(QueryApiRequest request) {
        if (CollUtil.isNotEmpty(request.getIds())) {
            return queryByIds(request.getIds(), false);
        }
        Map<String, String> appBizNameMap = appBizNameMap();
        getQueryParam(request);
        List<ApiQueryResponse> query = apiInfoService.getBaseMapper().query(request);
        for (ApiQueryResponse record : query) {
            record.setSensitiveRuleIds(StrUtil.isBlank(record.getTagIdsStr()) ? new HashSet<>() :
                    new HashSet<>(Arrays.asList(record.getTagIdsStr().split(","))));
            record.setUrl(record.getHostName() + record.getUri());
            record.setAppName(appBizNameMap.getOrDefault(record.getAppId(), "未知"));
        }
        return query;
    }

    /**
     * 根据apiId查询api信息
     *
     * @param apiId          apiId
     * @param containDeleted 是否包含已删除的api
     */
    public ApiQueryResponse queryById(String apiId, boolean containDeleted) {
        List<ApiQueryResponse> apis = queryByIds(Collections.singletonList(apiId), containDeleted);
        if (CollUtil.isNotEmpty(apis)) {
            return apis.get(0);
        } else {
            throw new BusinessException(ResultCodeEnum.API_NOT_EXISTED);
        }
    }

    /**
     * 根据 id 查询
     *
     * @param ids            id 集合
     * @param containDeleted 是否包含已删除的
     */
    public List<ApiQueryResponse> queryByIds(List<String> ids, boolean containDeleted) {
        if (CollUtil.isNotEmpty(ids)) {
            Map<String, String> appBizNameMap = appBizNameMap();
            List<ApiQueryResponse> apiInfos = apiInfoService.getBaseMapper().queryByIds(ids, containDeleted);
            for (ApiQueryResponse record : apiInfos) {
                // 可优化 split
                record.setSensitiveRuleIds(StrUtil.isBlank(record.getTagIdsStr()) ? new HashSet<>() :
                        new HashSet<>(Arrays.asList(record.getTagIdsStr().split(","))));
                record.setUrl(record.getHostName() + record.getUri());
                record.setAppName(appBizNameMap.getOrDefault(record.getAppId(), "未知"));
            }
            return apiInfos;
        } else {
            return new ArrayList<>();
        }
    }

    private Map<String, String> appBizNameMap() {
        List<ApplicationResponse> applicationResponses = applicationBusinessService.listWithBizName();
        // map,  applicationId 和 AppName
        return applicationResponses.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                ApplicationResponse::getName, (a, b) -> a));
    }

    public IPage<ApiQueryResponse> queryPage(QueryApiRequest request) {
        getQueryParam(request);

        IPage<ApiQueryResponse> pageData = apiInfoService.getBaseMapper()
                .query(new Page<>(request.getPageNum(), request.getPageSize()),
                        request);
        List<ApiQueryResponse> records = pageData.getRecords();
        Map<String, String> appBizNameMap = appBizNameMap();

        List<String> apiIds = records.stream().map(ApiQueryResponse::getId).collect(Collectors.toList());
        List<ApiDecrypt> apiDecrypts = apiDecryptService.getByApiIds(apiIds);
        Map<String, List<ApiDecrypt>> apiDecryptMap = MapUtil.grouping(ApiDecrypt::getApiId, apiDecrypts);

        // List<ApiInfoTree> apiInfoTrees = apiInfoService.listOfTree();
        // Map<String, ApiInfoTree> apiInfoTreeMap = apiInfoTrees.stream().collect(Collectors.toMap(ApiInfoTree::getId,
        //         apiInfoTree -> apiInfoTree));

        for (ApiQueryResponse record : records) {
            boolean isEncryptApi = apiDecryptMap.containsKey(record.getId());
            record.setSensitiveRuleIds(StrUtil.isBlank(record.getTagIdsStr()) ? new HashSet<>() :
                    new HashSet<>(Arrays.asList(record.getTagIdsStr().split(","))));
            record.setUrl(record.getHostName() + record.getUri());
            record.setAppName(appBizNameMap.getOrDefault(record.getAppId(), "未知"));
            record.setIsEncryptApi(isEncryptApi);
            if (isEncryptApi) {
                ApiDecrypt apiDecrypt = apiDecryptMap.get(record.getId()).get(0);
                record.setDecryptType(apiDecrypt.getDecryptType());
                record.setDecryptKey(apiDecrypt.getDecryptKey());
            }

            record.setVisitCount(Math.toIntExact(getVisitCount(record.getId())));
            // List<ApiInfo> children = apiInfoTreeMap.get(record.getId()).getChildren();
            // if (CollUtil.isNotEmpty(children)) {
            //     record.setVisitCount(Math.toIntExact(
            //             getVisitCount(children.stream().map(ApiInfo::getId).collect(Collectors.toList()))));
            // } else {
            //     record.setVisitCount(Math.toIntExact(
            //             getVisitCount(Collections.singletonList(record.getId()))));
            // }
        }
        return pageData;
    }

    private long getVisitCount(List<String> apiIds) {
        LocalDateTime now = LocalDateTime.now();
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(now.minusDays(30))
                .end(now)
                .build()
                .addMultipleQuery("apiId", apiIds);
        return nginxAccessLogService.count(queryDTO);
    }

    private long getVisitCount(String apiId) {
        if (StrUtil.isBlank(apiId)) {
            return 0L;
        }
        LocalDateTime now = LocalDateTime.now();
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(now.minusDays(30))
                .end(now)
                .build()
                .addQuery("apiId", apiId);
        return nginxAccessLogService.count(queryDTO);
    }

    private void getQueryParam(QueryApiRequest request) {
        request.format();
        Pair<LocalDateTime, LocalDateTime> timePair = DateTimeUtils.rangeTime(
                request.getRange(), request.getStartTime(), request.getEndTime());
        request.setStart(timePair.getLeft());
        request.setEnd(timePair.getRight());
        // 判断 url
        if (StrUtil.isBlank(request.getUri()) && StrUtil.isNotBlank(request.getUrl())) {
            if (!request.getUrl().startsWith("http")) {
                request.setUrl("http://" + request.getUrl());
            }
            URL url = ApiUrlUtils.getUrl(request.getUrl());
            if (url != null) {
                request.setHost(url.getHost());
                // .....
                request.setPort(url.getPort() == -1 ? request.getUrl().startsWith("https") ? 443 : 80 : url.getPort());
                request.setUri(url.getPath());
            }
        }
        // 适配数据库列
        if (request.getRangeType() != null) {
            if (!Arrays.asList("discoverTime", "onlineTime", "updateTime").contains(request.getRangeType())) {
                throw new BusinessException("非法的请求参数");
            }

            switch (request.getRangeType()) {
                case Constant.Api.RANGE_TYPE_DISCOVER:
                    request.setRangeType(Constant.Api.RANGE_TYPE_DISCOVER_DB);
                    break;
                case Constant.Api.RANGE_TYPE_ONLINE:
                    request.setRangeType(Constant.Api.RANGE_TYPE_ONLINE_DB);
                    break;
                case Constant.Api.RANGE_TYPE_UPDATE:
                    request.setRangeType(Constant.Api.RANGE_TYPE_UPDATE_DB);
                    break;
                default:
                    request.setRangeType("");
            }
        }
        if (CollUtil.isNotEmpty(request.getAppIds())) {
            List<String> appIds = applicationService.getIdsWithChildren(request.getAppIds());
            request.setAppIds(appIds);
        }

        if (request.getIsActive() != null) {
            List<String> aliveApiIds = getAliveApiIds();
            request.setAliveApiIds(aliveApiIds);
        }
    }


    public ApiStatResponse statById(String apiId) {
        apiInfoService.listOfTree();

        return nginxAccessLogService.stat(apiId);
    }

    /**
     * 统计风险
     */
    public ApiRiskSummaryResponse riskSummary() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfToday = now.withHour(0).withMinute(0).withSecond(0).withNano(0);

        List<ApiInfo> apiInfoList = apiInfoService.listApiWithDataScope();
        List<String> apiIds = apiInfoList.stream().map(ApiInfo::getId).collect(Collectors.toList());
        // 没有 api,统计个锤子
        if (CollUtil.isEmpty(apiIds)) {
            return ApiRiskSummaryResponse.builder()
                    .todayAttackApiCount(0)
                    .weekAttackApiCount(0)
                    .interceptAttackPercent(0)
                    .sensitiveApiCount(0)
                    .build();
        }

        int todayAttackCount = 0;
        int weekAttackCount = 0;

        List<ApiInfoTree> parentAndChildrenApis = apiInfoService.listOfTree();

        // ============= 今日攻击 =============
        Set<String> riskApiIdsOfToday = riskLogNewService.lambdaQuery()
                .select(RiskLogNew::getApiId)
                .between(RiskLogNew::getLogTime, startOfToday, now)
                .groupBy(RiskLogNew::getApiId).list().stream().map(RiskLogNew::getApiId).collect(Collectors.toSet());

        // ============= 本周攻击 =============
        Set<String> riskApiIdsOfWeek = riskLogNewService.lambdaQuery()
                .select(RiskLogNew::getApiId)
                .between(RiskLogNew::getLogTime, now.minusDays(7), now)
                .groupBy(RiskLogNew::getApiId).list().stream().map(RiskLogNew::getApiId).collect(Collectors.toSet());

        // ============= 本周拦截 =============
        int crsRejectRequestCount = riskLogNewService.lambdaQuery()
                .select(RiskLogNew::getLogId)
                .between(RiskLogNew::getLogTime, now.minusDays(7), now)
                .eq(RiskLogNew::getCrsDetectStatus, LogEnum.CrsDetectStatus.REJECT)
                .groupBy(RiskLogNew::getLogId).list().size();
        int riskRequestCount = riskLogNewService.lambdaQuery()
                .select(RiskLogNew::getLogId)
                .between(RiskLogNew::getLogTime, now.minusDays(7), now)
                .groupBy(RiskLogNew::getLogId).list().size();
        // ============= 拦截占比 =============
        float interceptAttackPercent = riskRequestCount == 0 ? 0 :
                Math.round(crsRejectRequestCount * 10000f / riskRequestCount) / 10000f;

        for (ApiInfoTree root : parentAndChildrenApis) {
            List<ApiInfo> children = root.getChildren();
            if (CollUtil.isEmpty(children)) {
                if (riskApiIdsOfToday.contains(root.getId())) {
                    todayAttackCount++;
                }
                if (riskApiIdsOfWeek.contains(root.getId())) {
                    weekAttackCount++;
                }
            } else {
                if (children.stream().anyMatch(api -> riskApiIdsOfToday.contains(api.getId()))) {
                    todayAttackCount++;
                }
                if (children.stream().anyMatch(api -> riskApiIdsOfWeek.contains(api.getId()))) {
                    weekAttackCount++;
                }
            }
        }
        // ============= 涉敏 api 总数 =============
        long sensitiveApiCount = getSensitiveApiCount();
        return ApiRiskSummaryResponse.builder()
                .todayAttackApiCount(todayAttackCount)
                .weekAttackApiCount(weekAttackCount)
                .interceptAttackPercent(interceptAttackPercent)
                .sensitiveApiCount(sensitiveApiCount)
                .build();
    }

    public long getSensitiveApiCount() {
        return apiInfoService.getSensitiveApiCount();
    }

    public ApiAttackStatResponse statAttackById(String apiId) {
        ApiInfo apiInfo = apiInfoService.getById(apiId);
        if (apiInfo == null) {
            throw new BusinessException(ResultCodeEnum.API_NOT_EXISTED);
        }
        return riskLogService.statAttack(apiId);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.API_INFO,
            description = "导入 api"
    )
    public List<?> importApi(String appId, MultipartFile file) {
        // 校验输入参数
        if (file == null || StrUtil.isBlank(file.getOriginalFilename())) {
            throw new BusinessException(ResultCodeEnum.INVALID_PARAMETER);
        }

        // 获取应用信息
        Optional<Application> appOp = applicationService.getByApplicationId(appId);
        if (!appOp.isPresent()) {
            throw new BusinessException("应用不存在");
        }
        Application application = appOp.get();

        if (application.getType() != ApplicationTypeEnum.BASE_API) {
            Optional<Application> baseApplication = applicationService.getBaseApplication(appId);
            if (baseApplication.isPresent()) {
                application = baseApplication.orElseThrow(() -> new BusinessException("基础应用不存在"));
            }
        }

        // 校验文件大小和类型
        String fileName = file.getOriginalFilename();
        long MAX_FILE_SIZE = 1024 * 1024 * 10;
        if (file.getSize() > MAX_FILE_SIZE) { // 假设 MAX_FILE_SIZE 是全局常量
            throw new BusinessException(ResultCodeEnum.FILE_TOO_LARGE);
        }
        FileType fileType = getFileType(fileName);
        if (fileType == null) {
            throw new BusinessException(ResultCodeEnum.NOT_SUPPORT_API_FILE_TYPE, "不支持的文件类型");
        }

        // 读取文件内容
        List<ApiImportFromFileDTO> objects;
        try {
            switch (fileType) {
                case JSON:
                    objects = readJson(file);
                    break;
                case XLSX:
                    objects = readExcel(file);
                    break;
                case CSV:
                    objects = readCsv(file);
                    break;
                case YAML:
                case YML:
                    objects = readYaml(file);
                    break;
                default:
                    throw new BusinessException(ResultCodeEnum.NOT_SUPPORT_API_FILE_TYPE, "不支持的文件类型");
            }
        } catch (Exception e) {
            log.error("文件[{}]读取失败", file.getOriginalFilename(), e);
            throw new BusinessException(ResultCodeEnum.FILE_READ_ERROR, e.getMessage());
        }

        // 处理对象列表
        Application finalApplication = application;
        objects.forEach(o -> {
            if (!o.getUri().startsWith("/")) {
                o.setUri("/" + o.getUri());
            }
            o.setAppId(finalApplication.getApplicationId());
            o.setUrl(finalApplication.getHost() + ":" + finalApplication.getPort() + o.getUri());
            o.setHost(finalApplication.getHost());
            o.setPort(Integer.valueOf(finalApplication.getPort()));
        });

        return objects;
    }

    // 提取文件类型判断逻辑
    private FileType getFileType(String fileName) {
        if (fileName == null) {
            return null;
        }
        if (fileName.endsWith(".json")) {
            return FileType.JSON;
        } else if (fileName.endsWith(".xlsx")) {
            return FileType.XLSX;
        } else if (fileName.endsWith(".csv")) {
            return FileType.CSV;
        } else if (fileName.endsWith(".yaml") || fileName.endsWith(".yml")) {
            return FileType.YAML; // 统一处理 .yaml 和 .yml
        }
        return null;
    }

    // 枚举文件类型
    enum FileType {
        JSON, XLSX, CSV, YAML, YML
    }


    private List<ApiImportFromFileDTO> readJson(MultipartFile file) {
        String content = null;
        try {
            content = new String(file.getBytes());
        } catch (IOException e) {
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        // 调用解析方法
        return SwaggerParserUtils.parseSwaggerJson(content);
    }


    private List<ApiImportFromFileDTO> readExcel(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 使用 Hutool 读取 Excel
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            reader.addHeaderAlias("API", "uri");
            reader.addHeaderAlias("请求方式", "httpMethod");
            reader.addHeaderAlias("名称", "name");
            reader.addHeaderAlias("上线时间", "onlineTime");
            reader.addHeaderAlias("备注", "remark");
            return reader.readAll(ApiImportFromFileDTO.class);
        } catch (Exception e) {
            log.error("解析 excel 文件失败", e);
            throw new BusinessException(ResultCodeEnum.IMPORT_EXCEL_FAILED);
        }
    }

    private List<ApiImportFromFileDTO> readCsv(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream();
             InputStreamReader reader = new InputStreamReader(inputStream)) {
            // 使用 Hutool 的 CsvReader 读取文件
            CsvReader csvReader = CsvUtil.getReader(new CsvReadConfig().setTrimField(true));
            return csvReader.read(reader, ApiImportFromFileDTO.class);
        } catch (Exception e) {
            log.error("解析 csv 文件失败", e);
            throw new BusinessException(ResultCodeEnum.IMPORT_EXCEL_FAILED);
        }
    }

    private List<ApiImportFromFileDTO> readYaml(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 使用 Hutool 读取 yml / yaml
            JSONObject load = YamlUtil.load(inputStream, JSONObject.class);
            return load.getBeanList("apis", ApiImportFromFileDTO.class);
        } catch (Exception e) {
            log.error("解析 yaml 文件失败", e);
            throw new BusinessException(ResultCodeEnum.IMPORT_YAML_FAILED);
        }
    }

    public void updateDecryptInfo(UpdateApiRequest apiRequest) {
        Boolean isEncryptApi = apiRequest.getIsEncryptApi();
        String apiId = apiRequest.getId();
        if (!isEncryptApi) {
            apiDecryptService.getByApiId(apiId).ifPresent((item) -> apiDecryptService.removeById(item.getId()));
            apiInfoService.getOptById(apiId)
                    .ifPresent((item) -> {
                        item.setEncrypted(false);
                        apiInfoService.updateById(item);
                    });
            return;
        }
        Optional<ApiDecrypt> decryptOptional = apiDecryptService.getByApiId(apiId);
        if (decryptOptional.isPresent()) {
            ApiDecrypt apiDecrypt = decryptOptional.get();
            apiDecrypt.setDecryptType(apiRequest.getDecryptType())
                    .setDecryptKey(apiRequest.getDecryptKey());
            apiDecryptService.updateById(apiDecrypt);
            return;
        }
        ApiDecrypt apiDecrypt = new ApiDecrypt(apiId, apiRequest.getDecryptType(), apiRequest.getDecryptKey());
        apiDecryptService.save(apiDecrypt);
        apiInfoService.getOptById(apiId)
                .ifPresent((item) -> {
                    item.setEncrypted(true);
                    apiInfoService.updateById(item);
                });
    }

    public IPage<LogQueryResponse> getLogHistory(QueryLogRequest request) {
        return logBizService.queryPageLog(request);
    }

    /**
     * api 概览统计, 排除已删除的api
     * 1. 总数
     * 2. 今日新增: 较昨日新增加的API数量
     * 3. 过去7日活跃: 过去七日被调用过的API数量
     * 4. 过去7日失活: 过去七日未被调用过的API数量
     * 5. 僵尸api: 过去三十日未被调用过的API数量
     */
    public ApiSummaryResponse summary() {
        LocalDateTime now = LocalDateTime.now();
        List<ApiInfo> apiInfoList = apiInfoService.listApiWithDataScope();
        List<String> apiIds = apiInfoList.stream().map(ApiInfo::getId).collect(Collectors.toList());
        if (apiIds.isEmpty()) {
            return ApiSummaryResponse.builder()
                    .apiCount(0)
                    .newApiCount(0)
                    .activeApiCount(0)
                    .inactiveApiCount(0)
                    .zombieApiCount(0)
                    .build();
        }

        List<ApiInfoTree> parentAndChildrenApis = apiInfoService.listOfTree();
        LocalDateTime startOfToday = now.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // ==================== 总数 ====================
        long total = apiInfoList.size();
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10000)
                .start(now.minusDays(7))
                .end(now)
                .build()
                .addMultipleQuery("apiId", apiIds);
        Set<String> activeApiIds = nginxAccessLogService.groupCount(queryDTO, "apiId")
                .stream()
                .filter(statCount -> statCount.getCount() > 0 &&
                        !"UNKNOWN".equalsIgnoreCase(statCount.getLabel())).map(StatCount::getLabel).collect(Collectors.toSet());

        // ==================== 7 - 30 -> 失活 ====================
        EsQueryDTO queryDTO2 = EsQueryDTO.builder()
                .queryCount(10000)
                .start(now.minusDays(30))
                .end(now)
                .build()
                .addMultipleQuery("apiId", apiIds);
        Set<String> activeApisOf30day = nginxAccessLogService.groupCount(queryDTO2, "apiId").stream()
                .filter(statCount -> statCount.getCount() > 0 && !"UNKNOWN".equalsIgnoreCase(statCount.getLabel()))
                .map(StatCount::getLabel).collect(Collectors.toSet());

        long newOfToday = 0;
        long activeOf7Days = 0;
        long activeOf30Days = 0;
        long inactiveOf7Days = 0;
        for (ApiInfoTree root : parentAndChildrenApis) {
            List<ApiInfo> children = root.getChildren();
            // ==================== 今日新增 ====================
            if (CollUtil.isEmpty(children)) {
                if (root.getDiscoverTime().isAfter(startOfToday)) {
                    newOfToday++;
                }
                if (activeApiIds.contains(root.getId())) {
                    activeOf7Days++;
                }
                if (activeApisOf30day.contains(root.getId())) {
                    activeOf30Days++;
                }
            } else {
                if (children.stream().anyMatch(api -> api.getDiscoverTime().isAfter(startOfToday))) {
                    newOfToday++;
                }
                if (children.stream().anyMatch(api -> activeApiIds.contains(api.getId()))) {
                    activeOf7Days++;
                }
                if (children.stream().anyMatch(api -> activeApisOf30day.contains(api.getId()))) {
                    activeOf30Days++;
                }
            }
        }

        // ==================== 僵尸api: 过去三十日未被调用过的API数量 ====================
        long zombie = total - activeOf7Days - inactiveOf7Days;

        return ApiSummaryResponse.builder()
                .apiCount(total)
                .newApiCount(newOfToday)
                .activeApiCount(activeOf7Days)
                .inactiveApiCount(activeOf30Days - activeOf7Days)
                .zombieApiCount(zombie)
                .build();
    }


    public List<String> getAliveApiIds() {
        LocalDateTime now = LocalDateTime.now();
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10000)
                .start(now.minusDays(7))
                .end(now)
                .build();
        return nginxAccessLogService.groupCount(queryDTO, "apiId")
                .stream()
                .map(StatCount::getLabel)
                .collect(Collectors.toList());
    }
}

