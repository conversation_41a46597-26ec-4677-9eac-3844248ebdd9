package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.RiskLogNew;

import java.util.Collection;
import java.util.List;

public interface RiskLogNewService extends IService<RiskLogNew> {
    List<RiskLogNew> queryLast7DaysRisk(List<String> distinctApplicationIds);

    RiskLogNew getByLogId(String riskId);

    boolean deleteByApiId(String apiId);

    void deleteByAppId(Collection<String> applicationId);

    boolean updateRiskLogsAppId(List<String> oldAppIds, String newAppId);

    void updateApiId(List<String> fromApiIds, String toApiId);
}
