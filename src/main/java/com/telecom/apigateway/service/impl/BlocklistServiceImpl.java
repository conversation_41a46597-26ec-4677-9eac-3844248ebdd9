package com.telecom.apigateway.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.BlocklistMapper;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.enums.BlocklistMatchEnum;
import com.telecom.apigateway.model.vo.request.BlocklistQueryRequest;
import com.telecom.apigateway.model.vo.request.BlocklistResponse;
import com.telecom.apigateway.service.BlocklistService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
public class BlocklistServiceImpl extends ServiceImpl<BlocklistMapper, Blocklist> implements BlocklistService {

    @Override
    public List<Blocklist> list() {
        return this.lambdaQuery()
                .eq(Blocklist::getDeleted, false)
                .orderByDesc(Blocklist::getUpdateTime)
                .list();
    }

    @Override
    public Blocklist add(Blocklist blocklist) {
        LocalDateTime now = LocalDateTime.now();
        blocklist.setCreateTime(now);
        blocklist.setUpdateTime(now);
        blocklist.setDeleted(false);
        this.save(blocklist);
        return blocklist;
    }

    @Override
    public Blocklist enable(String blockId) {
        Blocklist blocklist = this.lambdaQuery()
                .eq(Blocklist::getBlockId, blockId)
                .eq(Blocklist::getDeleted, false)
                .one();

        if (blocklist != null) {
            blocklist.setStatus("Active");
            blocklist.setUpdateTime(LocalDateTime.now());
            this.updateById(blocklist);
        }

        return blocklist;
    }

    @Override
    public Blocklist disable(String blockId) {
        Blocklist blocklist = this.lambdaQuery()
                .eq(Blocklist::getBlockId, blockId)
                .eq(Blocklist::getDeleted, false)
                .one();

        if (blocklist != null) {
            blocklist.setStatus("Inactive");
            blocklist.setUpdateTime(LocalDateTime.now());
            this.updateById(blocklist);
        }

        return blocklist;
    }

    @Override
    public void delete(String blockId) {
        this.lambdaUpdate()
                .eq(Blocklist::getBlockId, blockId)
                .set(Blocklist::getDeleted, true)
                .set(Blocklist::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void batchDelete(List<String> blockIds) {
        this.lambdaUpdate()
                .in(Blocklist::getBlockId, blockIds)
                .set(Blocklist::getDeleted, true)
                .set(Blocklist::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public Page<Blocklist> findAll(BlocklistQueryRequest pageRequest) {
        return this.lambdaQuery()
                .eq(Blocklist::getDeleted, false)
                .like(StringUtils.isNotBlank(pageRequest.getName()), Blocklist::getName, pageRequest.getName())
                .eq(StringUtils.isNotBlank(pageRequest.getType()), Blocklist::getType, pageRequest.getType())
                .eq(StringUtils.isNotBlank(pageRequest.getStatus()), Blocklist::getStatus, pageRequest.getStatus())
                .ge(pageRequest.getStartTime() != null, Blocklist::getUpdateTime, pageRequest.getStartTime())
                .le(pageRequest.getEndTime() != null, Blocklist::getUpdateTime, pageRequest.getEndTime())
                .orderByDesc(Blocklist::getType)
                .orderByDesc(Blocklist::getCreateTime)
                .page(new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()));
    }

    @Override
    public Optional<Blocklist> findById(String blockId) {
        Blocklist entity = this.lambdaQuery()
                .eq(Blocklist::getBlockId, blockId)
                .eq(Blocklist::getDeleted, false)
                .one();
        return Optional.ofNullable(entity);
    }

    @Override
    public List<Blocklist> findAllById(List<String> blockIds) {
        return this.lambdaQuery()
                .in(Blocklist::getBlockId, blockIds)
                .eq(Blocklist::getDeleted, false)
                .list();
    }

    @Override
    public void deleteById(String blockId) {
        this.lambdaUpdate()
                .eq(Blocklist::getBlockId, blockId)
                .set(Blocklist::getDeleted, true)
                .set(Blocklist::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void deleteAllById(List<String> blockIds) {
        this.lambdaUpdate()
                .in(Blocklist::getBlockId, blockIds)
                .set(Blocklist::getDeleted, true)
                .set(Blocklist::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void deleteByApiId(String apiId) {
        lambdaUpdate()
                .set(Blocklist::getDeleted, true)
                .like(Blocklist::getCondition, apiId)
                .update();
    }

    @Override
    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollUtil.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        List<Blocklist> blocklists = list();
        for (Blocklist blocklist : blocklists) {
            String conditionStr = blocklist.getCondition();
            List<BlocklistResponse.Condition> conditions =
                    JSONUtil.toList(conditionStr, BlocklistResponse.Condition.class);

            boolean isUpdated = false;

            for (BlocklistResponse.Condition condition : conditions) {
                if (condition.getTarget().equals(BlocklistMatchEnum.MatchTarget.API.getCode())) {
                    List<String> apiIds = new CopyOnWriteArrayList<>(StrUtil.split(condition.getValue(), ","));
                    for (int i = 0, apiIdsSize = apiIds.size(); i < apiIdsSize; i++) {
                        String apiId = apiIds.get(i);
                        if (fromApiIds.contains(apiId)) {
                            apiIds.set(i, toApiId);
                            isUpdated = true; // 标记为已更新
                        }
                    }
                    String newValue = StrUtil.join(",", new HashSet<>(apiIds));
                    condition.setValue(newValue);
                }
            }
            // 只有在更新后才保存
            if (isUpdated) {
                blocklist.setCondition(JSONUtil.toJsonStr(conditions));
                this.updateById(blocklist);
            }
        }
    }
}
