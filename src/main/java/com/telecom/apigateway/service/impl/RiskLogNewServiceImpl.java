package com.telecom.apigateway.service.impl;

import cloud.tianai.captcha.common.util.CollectionUtils;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.service.RiskLogNewService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Service
public class RiskLogNewServiceImpl extends ServiceImpl<RiskLogNewMapper, RiskLogNew> implements RiskLogNewService {
    @Override
    public List<RiskLogNew> queryLast7DaysRisk(List<String> distinctApplicationIds) {
        return this.lambdaQuery()
                .in(RiskLogNew::getAppId, distinctApplicationIds)
                .ge(RiskLogNew::getLogTime, LocalDateTime.now().minusDays(7))
                .list();
    }

    @Override
    public RiskLogNew getByLogId(String riskId) {
        return this.lambdaQuery()
                .eq(RiskLogNew::getLogId, riskId)
                .one();
    }

    @Override
    public boolean deleteByApiId(String apiId) {
        return lambdaUpdate().eq(RiskLogNew::getApiId, apiId).remove();
    }

    @Override
    public void deleteByAppId(Collection<String> applicationId) {
        if (CollectionUtils.isEmpty(applicationId)) return;
        lambdaUpdate().in(RiskLogNew::getAppId, applicationId).remove();
    }

    @Override
    public boolean updateRiskLogsAppId(List<String> oldAppIds, String newAppId) {
        if (CollectionUtils.isEmpty(oldAppIds) || StringUtils.isBlank(newAppId)) {
            return true;
        }

        return this.lambdaUpdate()
                .in(RiskLogNew::getAppId, oldAppIds)
                .set(RiskLogNew::getAppId, newAppId)
                .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                .set(RiskLogNew::getUpdateUser, StpUtil.getLoginIdAsString())
                .update();
    }

    @Override
    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollectionUtils.isEmpty(fromApiIds) || StringUtils.isBlank(toApiId)) {
            return;
        }
        this.lambdaUpdate()
                .in(RiskLogNew::getApiId, fromApiIds)
                .set(RiskLogNew::getApiId, toApiId)
                .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                .set(RiskLogNew::getUpdateUser, StpUtil.getLoginIdAsString())
                .update();
    }
}
