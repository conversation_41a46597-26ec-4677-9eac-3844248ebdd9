package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.SensitiveLog;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.enums.LogEnum;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLeakRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLogRequest;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiTriggerCountResponse;
import com.telecom.apigateway.model.vo.response.SensitiveLatestDataResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitChartResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitDetailResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitLogResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitResponse;
import com.telecom.apigateway.model.vo.response.SensitiveTopResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.utils.PageUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-22
 */
@Slf4j
@Service
public class SensitivePortraitDbService {
    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private ApiMergeService apiMergeService;

    public List<SensitiveLatestDataResponse> latestData() {
        List<SensitiveRule> rules = sensitiveRuleService.list();
        List<String> ruleIds =
                rules.stream().map(SensitiveRule::getId).collect(Collectors.toList());
        Map<String, SensitiveRule> ruleMap = rules.stream()
                .collect(Collectors.toMap(SensitiveRule::getId, sensitiveRule -> sensitiveRule));
        if (ruleIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<ApiInfo> apis = apiInfoService.list();
        Map<String, ApiInfo> apiMap = apis.stream().collect(Collectors.toMap(ApiInfo::getId, a -> a));
        if (apiMap.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, String> appMap = applicationBusinessService.listWithBizName()
                .stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                        ApplicationResponse::getName));

        return sensitiveLogService.lambdaQuery()
                .orderByDesc(SensitiveLog::getLogTime)
                .last("limit 4")
                .list()
                .stream()
                .map(sensitiveLog -> {
                    SensitiveLatestDataResponse result = new SensitiveLatestDataResponse();
                    result.setLogId(sensitiveLog.getLogId());
                    result.setKeyword(sensitiveLog.getContent());

                    ApiInfo apiInfo = apiMap.get(sensitiveLog.getApiId());
                    result.setApiId(sensitiveLog.getApiId());
                    result.setApiName(apiInfo.getName());
                    //
                    // String apiId = sensitiveLog.getApiId();
                    // ApiInfo apiInfo = apiMap.get(apiId);
                    // if (apiInfo.getMergeId() != null) {
                    //     apiInfo = apiMap.get(apiInfo.getMergeId());
                    // }
                    //
                    // result.setApiId(apiInfo.getId());
                    // result.setApiName(apiInfo.getName());
                    // todo app 合并后处理
                    result.setAppName(appMap.getOrDefault(sensitiveLog.getAppId(), "未知"));
                    result.setClientIp(sensitiveLog.getClientIp());
                    result.setLogTime(sensitiveLog.getLogTime());
                    result.setRequestState(sensitiveLog.getReqState());
                    SensitiveRule rule = ruleMap.get(sensitiveLog.getRuleId());
                    if (rule != null) {
                        result.setSensitiveRuleName(rule.getName());
                        result.setSensitiveLevel(rule.getLevel());
                    } else {
                        result.setSensitiveRuleName("未知");
                        result.setSensitiveLevel(3);
                    }
                    return result;
                })
                .collect(Collectors.toList());
    }

    public SensitiveTopResponse top() {
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData()) {
            return SensitiveTopResponse.empty();
        }

        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startOfToday = endTime.toLocalDate().atStartOfDay();

        List<SensitiveTopResponse.TopData> topDayCounts =
                sensitiveLogService.statTopRuleOfContent(startOfToday, endTime, 20);

        LocalDateTime startOfYesterday = startOfToday.minusDays(1);
        List<SensitiveTopResponse.TopData> yesterdayCounts =
                sensitiveLogService.statTopRuleOfContent(startOfYesterday, startOfToday, 20);

        LocalDateTime _30day = endTime.minusDays(30);
        List<SensitiveTopResponse.TopData> _30dayCounts = sensitiveLogService.statTopRuleOfContent(_30day, endTime, 5);

        int sumTodayCount = sensitiveLogService.sumContentOfRule(startOfToday, endTime);
        int sumYesterdayCount = sensitiveLogService.sumContentOfRule(startOfYesterday, startOfToday);
        int sumAllCount = sensitiveLogService.sumContentOfRule(_30day, endTime);

        _30dayCounts.forEach(result -> {
            result.setIncrease(0);
            topDayCounts.forEach(todayCount -> {
                if (todayCount.getLabel().equals(result.getLabel())) {
                    result.setIncrease(todayCount.getCount());
                }
            });
            yesterdayCounts.forEach(yesterdayCount -> {
                if (yesterdayCount.getLabel().equals(result.getLabel())) {
                    result.setIncrease(result.getIncrease() - yesterdayCount.getCount());
                }
            });
        });
        for (SensitiveTopResponse.TopData result : _30dayCounts) {
            result.setLabel(baseData.getAllRuleMap().getOrDefault(result.getLabel(), new SensitiveRule()).getName());
        }
        return new SensitiveTopResponse(sumAllCount, sumTodayCount - sumYesterdayCount, _30dayCounts);
    }

    public Page<?> search(String type, List<String> keywords, Integer pageNum, Integer pageSize) {
        if (StrUtil.isBlank(type)) {
            return Page.of(pageNum, pageSize, 0);
        }
        if (CollUtil.isEmpty(keywords)) {
            return Page.of(pageNum, pageSize, 0);
        }
        keywords = keywords.stream().map(String::trim).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            return Page.of(pageNum, pageSize, 0);
        }
        keywords.forEach(keyword -> {
            if (keyword.getBytes().length <= 1) {
                throw new BusinessException("查询内容长度过短(中文至少1,英文或字符至少2)");
            }
        });
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData()) {
            return Page.of(pageNum, pageSize, 0);
        }
        LambdaQueryChainWrapper<SensitiveLog> qw = getLambdaQueryOfSearch(keywords, baseData);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(30);
        switch (type.trim()) {
            case "content":
                return searchContent(qw, startTime, now, pageNum, pageSize, baseData);
            case "api":
                return searchApi(qw, startTime, now, pageNum, pageSize, baseData);
            case "app":
                return searchApp(qw, startTime, now, pageNum, pageSize, baseData);
            case "clientIp":
                return searchClientIp(qw, startTime, now, pageNum, pageSize, baseData);
        }
        throw new BusinessException("type error");
    }

    public Page<SensitivePortraitResponse.Content> searchContent(LambdaQueryChainWrapper<SensitiveLog> qw,
                                                                 LocalDateTime startTime,
                                                                 LocalDateTime endTime,
                                                                 Integer pageNum,
                                                                 Integer pageSize,
                                                                 BaseData baseData) {
        Page<SensitiveLog> page = qw
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .select(SensitiveLog::getContent)
                .groupBy(SensitiveLog::getContent)
                .page(new Page<>(pageNum, pageSize));
        List<SensitiveLog> records = page.getRecords();

        List<SensitivePortraitResponse.Content> results = new ArrayList<>();
        for (SensitiveLog record : records) {
            SensitivePortraitDetailResponse.Content detailOfContent =
                    getDetailOfContent(record.getContent(), startTime, endTime, baseData);

            SensitivePortraitResponse.Content result = new SensitivePortraitResponse.Content();
            result.setKeyword(record.getContent());
            result.setSensitiveCount(detailOfContent.getSensitiveCount());
            result.setSensitiveRules(detailOfContent.getSensitiveRules());
            result.setApis(
                    detailOfContent.getApis().stream()
                            .map(SensitivePortraitDetailResponse.Content.ApiAndApp::getApiName)
                            .collect(Collectors.toList())
            );
            result.setLastLogTime(detailOfContent.getLastLogTime());
            results.add(result);
        }
        return PageUtils.convertPage(page, results);
    }

    private Page<SensitivePortraitResponse.Api> searchApi(LambdaQueryChainWrapper<SensitiveLog> qw,
                                                          LocalDateTime startTime,
                                                          LocalDateTime endTime,
                                                          Integer pageNum,
                                                          Integer pageSize,
                                                          BaseData baseData) {
        // Page<SensitiveLog> page = sensitiveLogService.getBaseMapper().queryWithMergeApi(
        //         new Page<>(pageNum, pageSize)
        // );
        Page<SensitiveLog> page = qw
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .select(SensitiveLog::getApiId)
                .groupBy(SensitiveLog::getApiId)
                .page(new Page<>(pageNum, pageSize));
        List<SensitiveLog> records = page.getRecords();

        List<SensitivePortraitResponse.Api> results = new ArrayList<>();
        for (SensitiveLog record : records) {
            // 重新绑定为合并 api_id
            // ApiQueryResponse apiQueryResponse = baseData.getAllApiMap().get(record.getApiId());
            // if (apiQueryResponse.getMergeId() != null) {
            //     record.setApiId(apiQueryResponse.getMergeId());
            // }

            SensitivePortraitDetailResponse.Api detailOfApi =
                    getDetailOfApi(record.getApiId(), startTime, endTime, baseData);

            SensitivePortraitResponse.Api api = new SensitivePortraitResponse.Api();
            api.setApiId(detailOfApi.getApiId());
            api.setApiName(detailOfApi.getApiName());
            api.setSensitiveLevel(detailOfApi.getSensitiveLevel());
            api.setAppId(detailOfApi.getAppId());
            api.setAppName(detailOfApi.getAppName());
            api.setLastLogTime(detailOfApi.getLastLogTime());
            api.setSensitiveCount(Math.toIntExact(detailOfApi.getSensitiveCount()));
            api.setSensitiveRules(detailOfApi.getSensitiveRules());

            results.add(api);
        }
        return PageUtils.convertPage(page, results);
    }

    private Page<SensitivePortraitResponse.App> searchApp(LambdaQueryChainWrapper<SensitiveLog> qw,
                                                          LocalDateTime startTime,
                                                          LocalDateTime endTime,
                                                          Integer pageNum,
                                                          Integer pageSize,
                                                          BaseData baseData) {
        Page<SensitiveLog> page = qw
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .select(SensitiveLog::getAppId)
                .groupBy(SensitiveLog::getAppId)
                .page(new Page<>(pageNum, pageSize));
        List<SensitiveLog> records = page.getRecords();

        List<SensitivePortraitResponse.App> results = new ArrayList<>();
        for (SensitiveLog record : records) {
            SensitivePortraitDetailResponse.App detailOfApp =
                    getDetailOfApp(record.getAppId(), startTime, endTime, baseData);

            SensitivePortraitResponse.App app = new SensitivePortraitResponse.App();
            app.setAppId(detailOfApp.getAppId());
            app.setAppName(detailOfApp.getAppName());
            app.setParentAppName(detailOfApp.getParentAppName());
            app.setSensitiveRules(detailOfApp.getSensitiveRules());
            app.setLastLogTime(detailOfApp.getLastLogTime());

            results.add(app);
        }
        return PageUtils.convertPage(page, results);
    }

    private Page<SensitivePortraitResponse.ClientIp> searchClientIp(LambdaQueryChainWrapper<SensitiveLog> qw,
                                                                    LocalDateTime startTime,
                                                                    LocalDateTime endTime,
                                                                    Integer pageNum,
                                                                    Integer pageSize,
                                                                    BaseData baseData) {
        Page<SensitiveLog> page = qw
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .select(SensitiveLog::getClientIp)
                .groupBy(SensitiveLog::getClientIp)
                .page(new Page<>(pageNum, pageSize));
        List<SensitiveLog> records = page.getRecords();

        List<SensitivePortraitResponse.ClientIp> results = new ArrayList<>();
        for (SensitiveLog record : records) {
            SensitivePortraitResponse.ClientIp clientIp = new SensitivePortraitResponse.ClientIp();
            SensitivePortraitDetailResponse.ClientIp detailOfClientIp =
                    getDetailOfClientIp(record.getClientIp(), startTime, endTime, baseData);

            clientIp.setClientIp(detailOfClientIp.getClientIp());
            clientIp.setAddr(detailOfClientIp.getAddr());
            // todo isp
            clientIp.setIsp(detailOfClientIp.getAddr());
            clientIp.setLastLogTime(detailOfClientIp.getLastLogTime());
            clientIp.setSensitiveCount(detailOfClientIp.getSensitiveCount());
            results.add(clientIp);
        }
        return PageUtils.convertPage(page, results);
    }

    SensitivePortraitDetailResponse.Content getDetailOfContent(String content,
                                                               LocalDateTime startTime,
                                                               LocalDateTime endTime,
                                                               BaseData baseData) {
        List<SensitiveLog> slogs = sensitiveLogService.lambdaQuery()
                .eq(SensitiveLog::getContent, content)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByDesc(SensitiveLog::getLogTime)
                .list();
        // 重新绑定为合并 api_id
        // for (SensitiveLog slog : slogs) {
        //     ApiQueryResponse api = baseData.getAllApiMap().get(slog.getApiId());
        //     if (api.getMergeId() != null) {
        //         slog.setApiId(api.getMergeId());
        //     }
        // }

        SensitiveLog sensitiveLog = slogs.get(0);

        SensitivePortraitDetailResponse.Content contentResult = new SensitivePortraitDetailResponse.Content();
        contentResult.setKeyword(content);
        contentResult.setLastLogTime(sensitiveLog.getLogTime());

        // todo 计数: 请求次数还是数据次数
        contentResult.setSensitiveCount(
                (int) slogs.stream().map(SensitiveLog::getLogId).distinct().count()
        );
        contentResult.setSensitiveApiCount(
                (int) slogs.stream().map(SensitiveLog::getApiId).distinct().count()
        );
        contentResult.setClientIpCount(
                (int) slogs.stream().map(SensitiveLog::getClientIp).distinct().count()
        );

        contentResult.setApis(
                slogs.stream()
                        .map(SensitiveLog::getApiId)
                        .distinct()
                        .map(apiId -> {
                            ApiQueryResponse api = baseData.getAllApiMap().get(apiId);
                            ApplicationResponse app = baseData.getAllBizNameAppMap().get(api.getAppId());
                            return SensitivePortraitDetailResponse.Content.ApiAndApp.builder()
                                    .apiId(apiId)
                                    .apiName(api.getName())
                                    .appId(app.getApplicationId())
                                    .appName(app.getName()).build();
                        })
                        .collect(Collectors.toList())
        );
        contentResult.setSensitiveRules(
                slogs.stream()
                        .map(SensitiveLog::getRuleId)
                        .distinct()
                        .filter(ruleId -> baseData.getAllRuleMap().containsKey(ruleId))
                        .map(ruleId -> new SensitivePortraitResponse.Content.RuleAndLevel(baseData.getAllRuleMap().get(ruleId)))
                        .sorted(Comparator.comparingInt(SensitivePortraitResponse.Content.RuleAndLevel::getSensitiveLevel).reversed())
                        .collect(Collectors.toList())
        );
        return contentResult;
    }

    SensitivePortraitDetailResponse.Api getDetailOfApi(String apiId,
                                                       LocalDateTime startTime,
                                                       LocalDateTime endTime,
                                                       BaseData baseData) {
        // List<String> apiIds = baseData.getMergeApiId(apiId);

        List<SensitiveLog> slogs = sensitiveLogService.lambdaQuery()
                .eq(SensitiveLog::getApiId, apiId)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByDesc(SensitiveLog::getLogTime)
                .list();
        SensitiveLog sensitiveLog = slogs.get(0);

        SensitivePortraitDetailResponse.Api api = new SensitivePortraitDetailResponse.Api();

        api.setApiId(apiId);
        api.setApiName(
                baseData.getAllApiMap().get(sensitiveLog.getApiId()).getName()
        );
        api.setAppId(
                baseData.getAllApiMap().get(sensitiveLog.getApiId()).getAppId()
        );
        api.setAppName(
                baseData.getAllBizNameAppMap().get(sensitiveLog.getAppId()).getName()
        );
        api.setSensitiveLevel(
                baseData.getAllApiMap().get(sensitiveLog.getApiId()).getSensitiveLevel()
        );
        api.setSensitiveCount(
                slogs.stream().map(SensitiveLog::getLogId).distinct().count()
        );
        api.setLastLogTime(sensitiveLog.getLogTime());
        // rules
        api.setSensitiveRules(
                slogs.stream()
                        .map(SensitiveLog::getRuleId)
                        .distinct()
                        .map(ruleId -> baseData.getAllRuleMap().get(ruleId).getName())
                        .collect(Collectors.toList())
        );

        api.setClientIpCount(
                slogs.stream().map(SensitiveLog::getClientIp).distinct().count()
        );
        api.setSensitiveContentCount(
                slogs.stream().map(SensitiveLog::getContent).distinct().count()
        );
        return api;
    }

    SensitivePortraitDetailResponse.App getDetailOfApp(String appId,
                                                       LocalDateTime startTime,
                                                       LocalDateTime endTime,
                                                       BaseData baseData) {
        List<SensitiveLog> slogs = sensitiveLogService.lambdaQuery()
                .eq(SensitiveLog::getAppId, appId)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByDesc(SensitiveLog::getLogTime)
                .list();
        SensitiveLog sensitiveLog = slogs.get(0);

        SensitivePortraitDetailResponse.App app = new SensitivePortraitDetailResponse.App();
        Application application = baseData.getAllAppMap().get(sensitiveLog.getAppId());
        app.setAppId(sensitiveLog.getAppId());
        app.setAppName(application.getName());
        app.setParentAppName("无");
        baseData.getAllApps().stream().filter(app1 -> app1.getApplicationId().equals(application.getParentId()))
                .findFirst().ifPresent(app1 -> app.setParentAppName(app1.getName()));
        app.setLastLogTime(sensitiveLog.getLogTime());
        // rules
        app.setSensitiveRules(
                slogs.stream()
                        .map(SensitiveLog::getRuleId)
                        .distinct()
                        .map(ruleId -> baseData.getAllRuleMap().get(ruleId).getName())
                        .collect(Collectors.toList())
        );
        app.setClientIpCount(
                (int) slogs.stream().map(SensitiveLog::getClientIp).distinct().count()
        );
        app.setSensitiveContentCount(
                (int) slogs.stream().map(SensitiveLog::getContent).distinct().count()
        );
        app.setSensitiveApiCount(
                (int) slogs.stream().map(SensitiveLog::getApiId).distinct().count()
        );
        app.setSensitiveCount(
                (int) slogs.stream().map(SensitiveLog::getLogId).distinct().count()
        );
        app.setLastLogTime(sensitiveLog.getLogTime());
        return app;
    }

    SensitivePortraitDetailResponse.ClientIp getDetailOfClientIp(String ClientIp,
                                                                 LocalDateTime startTime,
                                                                 LocalDateTime endTime,
                                                                 BaseData baseData) {
        List<SensitiveLog> slogs = sensitiveLogService.lambdaQuery()
                .eq(SensitiveLog::getClientIp, ClientIp)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByDesc(SensitiveLog::getLogTime)
                .list();
        SensitiveLog sensitiveLog = slogs.get(0);

        SensitivePortraitDetailResponse.ClientIp clientIp = new SensitivePortraitDetailResponse.ClientIp();

        clientIp.setClientIp(sensitiveLog.getClientIp());
        clientIp.setAddr(sensitiveLog.getAddr());
        // todo 获取isp
        clientIp.setIsp(sensitiveLog.getAddr());
        clientIp.setLastLogTime(sensitiveLog.getLogTime());
        clientIp.setSensitiveRules(
                slogs.stream()
                        .map(SensitiveLog::getRuleId)
                        .distinct()
                        .map(ruleId -> baseData.getAllRuleMap().get(ruleId).getName())
                        .collect(Collectors.toList())
        );
        clientIp.setSensitiveCount((int) slogs.stream().map(SensitiveLog::getLogId).distinct().count());
        clientIp.setSensitiveContentCount((int) slogs.stream().map(SensitiveLog::getContent).distinct().count());
        clientIp.setSensitiveApiCount((int) slogs.stream().map(SensitiveLog::getApiId).distinct().count());
        return clientIp;
    }

    private LambdaQueryChainWrapper<SensitiveLog> getLambdaQueryOfSearch(List<String> keywords, BaseData baseData) {
        LocalDateTime now = LocalDateTime.now();

        // 支持敏感数据、涉敏标签、API、应用、IP的搜索
        LambdaQueryChainWrapper<SensitiveLog> qw = new LambdaQueryChainWrapper<>(sensitiveLogService.getBaseMapper());
        qw.between(SensitiveLog::getLogTime, now.minusDays(30), now);

        for (String keyword : keywords) {
            List<String> appIds = new ArrayList<>();
            baseData.getAllApps().stream()
                    .filter(ele -> ele.getName().contains(keyword) || ele.getHttpHost().contains(keyword))
                    .map(Application::getApplicationId)
                    .forEach(ele -> appIds.addAll(getAppIdWithChildren(baseData.getAllApps(), ele)));

            List<String> apiIds = baseData.getAllApis().stream()
                    .filter(ele -> ele.getName().contains(keyword) || ele.getHosts().contains(keyword))
                    .map(ApiQueryResponse::getId)
                    .collect(Collectors.toList());

            List<String> ruleIds = baseData.getAllRules().stream()
                    .filter(ele -> ele.getName().contains(keyword))
                    .map(SensitiveRule::getId)
                    .collect(Collectors.toList());

            qw.and(
                    i -> i
                            .or().like(SensitiveLog::getContent, keyword) // 敏感数据
                            .or().in(CollUtil.isNotEmpty(ruleIds), SensitiveLog::getRuleId, ruleIds)  // 涉敏标签
                            .or().in(CollUtil.isNotEmpty(apiIds), SensitiveLog::getApiId, apiIds) // API
                            .or().in(CollUtil.isNotEmpty(appIds), SensitiveLog::getAppId, appIds) // 应用
                            .or().like(SensitiveLog::getClientIp, keyword) // IP
            );
        }
        return qw;
    }

    private QueryChainWrapper<SensitiveLog> getQueryOfSearch(List<String> keywords, BaseData baseData) {
        LocalDateTime now = LocalDateTime.now();

        // 支持敏感数据、涉敏标签、API、应用、IP的搜索
        QueryChainWrapper<SensitiveLog> qw = new QueryChainWrapper<>(sensitiveLogService.getBaseMapper());
        qw.between("log_time", now.minusDays(30), now);

        for (String keyword : keywords) {
            List<String> appIds = new ArrayList<>();
            baseData.getAllApps().stream()
                    .filter(ele -> ele.getName().contains(keyword) || ele.getHttpHost().contains(keyword))
                    .map(Application::getApplicationId)
                    .forEach(ele -> appIds.addAll(getAppIdWithChildren(baseData.getAllApps(), ele)));

            List<String> apiIds = baseData.getAllApis().stream()
                    .filter(ele -> ele.getName().contains(keyword) || ele.getHosts().contains(keyword))
                    .map(ApiQueryResponse::getId)
                    .collect(Collectors.toList());

            List<String> ruleIds = baseData.getAllRules().stream()
                    .filter(ele -> ele.getName().contains(keyword))
                    .map(SensitiveRule::getId)
                    .collect(Collectors.toList());

            qw.and(
                    i -> i
                            .or().like("content", keyword) // 敏感数据
                            .or().in(CollUtil.isNotEmpty(ruleIds), "rule_id", ruleIds)  // 涉敏标签
                            .or().in(CollUtil.isNotEmpty(apiIds), "api_id", apiIds) // API
                            .or().in(CollUtil.isNotEmpty(appIds), "appId", appIds) // 应用
                            .or().like("client_ip", keyword) // IP
            );
        }
        return qw;
    }


    public Object getDetail(String type, String keyword, LocalDateTime startTime, LocalDateTime endTime) {
        if (StrUtil.isBlank(type)) {
            return null;
        }
        if (StrUtil.isBlank(keyword)) {
            return null;
        }
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData() || !baseData.containsTarget(type, keyword)) {
            return null;
        }
        switch (type.trim()) {
            case "content":
                return getDetailOfContent(keyword, startTime, endTime, baseData);
            case "api":
                return getDetailOfApi(keyword, startTime, endTime, baseData);
            case "app":
                return getDetailOfApp(keyword, startTime, endTime, baseData);
            case "clientIp":
                return getDetailOfClientIp(keyword, startTime, endTime, baseData);
        }
        throw new BusinessException("type error");
    }


    public Object getCharts(String type, String keyword, LocalDateTime startTime, LocalDateTime endTime) {
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData() || !baseData.containsTarget(type, keyword)) {
            return null;
        }
        if (StrUtil.isBlank(keyword)) {
            return null;
        }
        switch (type.trim()) {
            case "content":
                return SensitivePortraitChartResponse.Content.builder()
                        .trends(statTrendByContent(keyword, startTime, endTime))
                        .topApis(statApiByContent(keyword, startTime, endTime, baseData))
                        .topClientIps(statClientIpByContent(keyword, startTime, endTime))
                        .build();
            case "api":
                return SensitivePortraitChartResponse.Api.builder()
                        .trends(statTrendByApiId(keyword, startTime, endTime))
                        .topRules(statRuleByApiId(keyword, startTime, endTime))
                        .topContents(statContentByApiId(keyword, startTime, endTime))
                        .topClientIps(statClientIpByApiId(keyword, startTime, endTime))
                        .build();
            case "app":
                return SensitivePortraitChartResponse.App.builder()
                        .trends(statTrendByAppId(keyword, startTime, endTime))
                        .topApis(statApiByAppId(keyword, startTime, endTime, baseData))
                        .topRules(statRuleByAppId(keyword, startTime, endTime))
                        .topContents(statContentByAppId(keyword, startTime, endTime))
                        .topClientIps(statClientIpByAppId(keyword, startTime, endTime))
                        .build();
            case "clientIp":
                return SensitivePortraitChartResponse.ClientIp.builder()
                        .trends(statTrendByClientIp(keyword, startTime, endTime))
                        .topApis(statApiByClientIp(keyword, startTime, endTime, baseData))
                        .topRules(statContentByClientIp(keyword, startTime, endTime))
                        .topContents(statRuleByClientIp(keyword, startTime, endTime))
                        .build();
        }
        throw new BusinessException("type error");
    }

    private List<SensitiveApiTriggerCountResponse> statApiByContent(String content,
                                                                    LocalDateTime startTime,
                                                                    LocalDateTime endTime,
                                                                    BaseData baseData) {
        return statApi(content, null, null, startTime, endTime, baseData);
    }

    private List<SensitiveApiTriggerCountResponse> statApiByAppId(String appId,
                                                                  LocalDateTime startTime,
                                                                  LocalDateTime endTime,
                                                                  BaseData baseData) {
        return statApi(null, appId, null, startTime, endTime, baseData);
    }

    private List<SensitiveApiTriggerCountResponse> statApiByClientIp(String clientIp,
                                                                     LocalDateTime startTime,
                                                                     LocalDateTime endTime,
                                                                     BaseData baseData) {
        return statApi(null, null, clientIp, startTime, endTime, baseData);
    }

    private List<SensitiveApiTriggerCountResponse> statApi(String content,
                                                           String appId,
                                                           String clientIp,
                                                           LocalDateTime startTime,
                                                           LocalDateTime endTime,
                                                           BaseData baseData) {
        List<Map<String, Object>> list = sensitiveLogService
                .getBaseMapper()
                .selectMaps(
                        new QueryWrapper<SensitiveLog>()
                                .select("api_id", "count(1) as count")
                                .between("log_time", startTime, endTime)
                                .eq(StrUtil.isNotBlank(content), "content", content)
                                .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                .eq(StrUtil.isNotBlank(clientIp), "client_ip", clientIp)
                                .groupBy("api_id")
                                .orderByDesc("count")
                                .last("limit 10")
                );

        Map<String, ApplicationResponse> allBizNameAppMap = baseData.getAllBizNameAppMap();
        List<SensitiveApiTriggerCountResponse> results = new ArrayList<>();

        for (Map<String, Object> slog : list) {
            ApiQueryResponse apiInfo = baseData.getAllApiMap().get((String) slog.get("api_id"));
            if (apiInfo == null) continue;
            ApplicationResponse biaNameApp = allBizNameAppMap.get(apiInfo.getAppId());
            results.add(
                    SensitiveApiTriggerCountResponse.builder()
                            .appId(biaNameApp.getApplicationId())
                            .appName(biaNameApp.getName())
                            .apiId(apiInfo.getId())
                            .apiName(biaNameApp.getName() + ":" + apiInfo.getName())
                            .count(Integer.parseInt(slog.get("count").toString()))
                            .build()
            );
        }
        return results;
    }

    private List<StatCount> statClientIpByContent(String content,
                                                  LocalDateTime startTime,
                                                  LocalDateTime endTime) {
        return statClientIp(content, null, null, startTime, endTime);
    }

    private List<StatCount> statClientIpByApiId(String apiId,
                                                LocalDateTime startTime,
                                                LocalDateTime endTime) {
        return statClientIp(null, apiId, null, startTime, endTime);
    }

    private List<StatCount> statClientIpByAppId(String appId,
                                                LocalDateTime startTime,
                                                LocalDateTime endTime) {
        return statClientIp(null, null, appId, startTime, endTime);
    }

    private List<StatCount> statClientIp(String content,
                                         String apiId,
                                         String appId,
                                         LocalDateTime startTime,
                                         LocalDateTime endTime) {
        List<Map<String, Object>> list = sensitiveLogService
                .getBaseMapper()
                .selectMaps(
                        new QueryWrapper<SensitiveLog>()
                                .select("client_ip", "count(1) as count")
                                .between("log_time", startTime, endTime)
                                .eq(StrUtil.isNotBlank(content), "content", content)
                                .eq(StrUtil.isNotBlank(apiId), "api_id", apiId)
                                .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                .groupBy("client_ip")
                                .orderByDesc("count")
                                .last("limit 10")
                );

        return list.stream()
                .map(slog ->
                        new StatCount(
                                (String) slog.get("client_ip"),
                                Integer.parseInt(slog.get("count").toString()))
                )
                .collect(Collectors.toList());
    }

    private List<StatCount> statRuleByContent(String content,
                                              LocalDateTime startTime,
                                              LocalDateTime endTime) {
        return statRule(content, null, null, null, startTime, endTime);
    }

    private List<StatCount> statRuleByApiId(String apiId,
                                            LocalDateTime startTime,
                                            LocalDateTime endTime) {
        return statRule(null, apiId, null, null, startTime, endTime);
    }

    private List<StatCount> statRuleByAppId(String appId,
                                            LocalDateTime startTime,
                                            LocalDateTime endTime) {
        return statRule(null, null, appId, null, startTime, endTime);
    }

    private List<StatCount> statRuleByClientIp(String clientIp,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        return statRule(null, null, null, clientIp, startTime, endTime);
    }

    private List<StatCount> statRule(String content,
                                     String apiId,
                                     String appId,
                                     String clientIp,
                                     LocalDateTime startTime,
                                     LocalDateTime endTime) {
        List<Map<String, Object>> list = sensitiveLogService
                .getBaseMapper()
                .selectMaps(
                        new QueryWrapper<SensitiveLog>()
                                .select("rule_id", "count(1) as count")
                                .between("log_time", startTime, endTime)
                                .eq(StrUtil.isNotBlank(content), "content", content)
                                .eq(StrUtil.isNotBlank(apiId), "api_id", apiId)
                                .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                .eq(StrUtil.isNotBlank(clientIp), "client_ip", clientIp)
                                .groupBy("rule_id")
                                .orderByDesc("count")
                                .last("limit 10")
                );

        return list.stream()
                .map(slog ->
                        new StatCount(
                                getBaseData().getAllRuleMap().get((String) slog.get("rule_id")).getName(),
                                Integer.parseInt(slog.get("count").toString()))
                )
                .collect(Collectors.toList());
    }

    private List<StatCount> statContentByApiId(String apiId,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        return statContent(apiId, null, null, startTime, endTime);
    }

    private List<StatCount> statContentByAppId(String appId,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        return statContent(null, appId, null, startTime, endTime);
    }

    private List<StatCount> statContentByClientIp(String clientIp,
                                                  LocalDateTime startTime,
                                                  LocalDateTime endTime) {
        return statContent(null, null, clientIp, startTime, endTime);
    }

    private List<StatCount> statContent(String apiId,
                                        String appId,
                                        String clientIp,
                                        LocalDateTime startTime,
                                        LocalDateTime endTime) {
        List<Map<String, Object>> list = sensitiveLogService
                .getBaseMapper()
                .selectMaps(
                        new QueryWrapper<SensitiveLog>()
                                .select("content", "count(1) as count")
                                .between("log_time", startTime, endTime)
                                .eq(StrUtil.isNotBlank(apiId), "api_id", apiId)
                                .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                .eq(StrUtil.isNotBlank(clientIp), "client_ip", clientIp)
                                .groupBy("content")
                                .orderByDesc("count")
                                .last("limit 10")
                );

        return list.stream()
                .map(slog ->
                        new StatCount(
                                (String) slog.get("content"),
                                Integer.parseInt(slog.get("count").toString()))
                )
                .collect(Collectors.toList());
    }


    private List<StatCount> statTrendByContent(String content,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        return statTrend(content, null, null, null, startTime, endTime);
    }

    private List<StatCount> statTrendByApiId(String apiId,
                                             LocalDateTime startTime,
                                             LocalDateTime endTime) {
        return statTrend(null, apiId, null, null, startTime, endTime);
    }

    private List<StatCount> statTrendByAppId(String appId,
                                             LocalDateTime startTime,
                                             LocalDateTime endTime) {
        return statTrend(null, null, appId, null, startTime, endTime);
    }

    private List<StatCount> statTrendByClientIp(String clientIp,
                                                LocalDateTime startTime,
                                                LocalDateTime endTime) {
        return statTrend(null, null, null, clientIp, startTime, endTime);
    }

    private List<StatCount> statTrend(String content,
                                      String apiId,
                                      String appId,
                                      String clientIp,
                                      LocalDateTime startTime0,
                                      LocalDateTime endTime) {
        boolean isMonth = startTime0.plusDays(90).isBefore(endTime);

        LocalDateTime startTime = startTime0;
        String statFormat = isMonth ? "yyyy-MM月" : "yyyy-MM-dd日";
        String labelFormat = isMonth ? "MM月" : "dd日";

        List<StatCount> statCounts = new ArrayList<>();
        if (isMonth) {
            List<Map<String, Object>> months = sensitiveLogService
                    .getBaseMapper()
                    .selectMaps(
                            new QueryWrapper<SensitiveLog>()
                                    .select("to_char(date_trunc('month', log_time), 'yyyy-mm月') as month",
                                            "count(distinct log_id) as count")
                                    .between("log_time", startTime, endTime)
                                    .eq(StrUtil.isNotBlank(content), "content", content)
                                    .eq(StrUtil.isNotBlank(apiId), "api_id", apiId)
                                    .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                    .eq(StrUtil.isNotBlank(clientIp), "client_ip", clientIp)
                                    .groupBy("month")
                                    .orderByAsc("month")
                    );
            while (startTime.withDayOfMonth(1).isBefore(endTime.withDayOfMonth(1)) ||
                    startTime.withDayOfMonth(1).isEqual(endTime.withDayOfMonth(1))) {
                String yyyyMM = startTime.format(DateTimeFormatter.ofPattern(statFormat));
                StatCount st = new StatCount(startTime.format(DateTimeFormatter.ofPattern(labelFormat)), 0);
                months.stream().filter(statCount -> statCount.get("month").equals(yyyyMM))
                        .findFirst()
                        .ifPresent(statCount -> st.setCount(Integer.parseInt(statCount.get("count").toString())));
                statCounts.add(st);
                startTime = startTime.plusMonths(1);
            }
        } else {
            List<Map<String, Object>> months = sensitiveLogService
                    .getBaseMapper()
                    .selectMaps(
                            new QueryWrapper<SensitiveLog>()
                                    .select("to_char(date_trunc('day', log_time), 'yyyy-mm-dd日') as day",
                                            "count(distinct log_id) as count")
                                    .between("log_time", startTime, endTime)
                                    .eq(StrUtil.isNotBlank(content), "content", content)
                                    .eq(StrUtil.isNotBlank(apiId), "api_id", apiId)
                                    .eq(StrUtil.isNotBlank(appId), "app_id", appId)
                                    .eq(StrUtil.isNotBlank(clientIp), "client_ip", clientIp)
                                    .groupBy("day")
                                    .orderByAsc("day")
                    );
            while (startTime.isBefore(endTime) || startTime.isEqual(endTime)) {
                String yyyyMMdd = startTime.format(DateTimeFormatter.ofPattern(statFormat));
                StatCount st = new StatCount(startTime.format(DateTimeFormatter.ofPattern(labelFormat)), 0);
                months.stream().filter(statCount -> statCount.get("day").equals(yyyyMMdd))
                        .findFirst()
                        .ifPresent(statCount -> st.setCount(Integer.parseInt(statCount.get("count").toString())));
                statCounts.add(st);
                startTime = startTime.plusDays(1);
            }
        }
        return statCounts;
    }

    public Page<SensitivePortraitLogResponse> queryLog(QuerySensitivePortraitLogRequest request) {
        request.format();
        LambdaQueryChainWrapper<SensitiveLog> query = sensitiveLogService.lambdaQuery();
        if (CollUtil.isNotEmpty(request.getAppId())) {
            List<String> idsWithChildren = applicationService.getIdsWithChildren(request.getAppId(), false);
            if (idsWithChildren.isEmpty()) {
                idsWithChildren.add("-1");
            }
            query.in(SensitiveLog::getAppId, idsWithChildren);
        }
        if (CollUtil.isNotEmpty(request.getSensitiveLevel())) {
            List<SensitiveRule> rules = sensitiveRuleService.list();
            List<String> ruleIds =
                    rules.stream().filter(rule -> request.getSensitiveLevel().contains(rule.getLevel()))
                            .map(SensitiveRule::getId).collect(Collectors.toList());
            if (ruleIds.isEmpty()) {
                ruleIds.add("-1");
            }
            query.in(CollUtil.isNotEmpty(ruleIds), SensitiveLog::getRuleId, ruleIds);
        }

        query.in(CollUtil.isNotEmpty(request.getApiId()), SensitiveLog::getApiId, request.getApiId());
        query.in(CollUtil.isNotEmpty(request.getRuleId()), SensitiveLog::getRuleId, request.getRuleId());

        if (CollUtil.isNotEmpty(request.getRequestState())) {
            if (request.getRequestState().size() == 1) {
                switch (request.getRequestState().get(0)) {
                    case NORMAL:
                        query.eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.PASS);
                        query.eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.PASS);
                        query.eq(SensitiveLog::getAbnormalBehaviorDetectStatus,
                                LogEnum.AbnormalBehaviorDetectStatus.PASS);
                        break;
                    case BLOCKLIST_ALLOW:
                        query.eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.ALLOW);
                        break;
                    case BLOCKLIST_REJECT:
                        query.eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.REJECT);
                        break;
                    case RISK_REJECT:
                        query.eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.REJECT);
                        break;
                    case RISK_LOG:
                        query.eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.LOG);
                        break;
                    case ABRT_REJECT:
                        query.eq(SensitiveLog::getAbnormalBehaviorDetectStatus,
                                LogEnum.AbnormalBehaviorDetectStatus.REJECT);
                        break;
                    case ABRT_LOG:
                        query.eq(SensitiveLog::getAbnormalBehaviorDetectStatus,
                                LogEnum.AbnormalBehaviorDetectStatus.LOG);
                        break;
                }
            } else {
                // 我也看不懂
                query.and(i ->
                        request.getRequestState().forEach(requestState -> {
                            switch (requestState) {
                                case NORMAL:
                                    i.or().eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.PASS)
                                            .eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.PASS)
                                            .eq(SensitiveLog::getAbnormalBehaviorDetectStatus,
                                                    LogEnum.AbnormalBehaviorDetectStatus.PASS);
                                    break;
                                case BLOCKLIST_ALLOW:
                                    i.or().eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.ALLOW);
                                    break;
                                case BLOCKLIST_REJECT:
                                    i.or().eq(SensitiveLog::getWafDetectStatus, LogEnum.WafDetectStatus.REJECT);
                                    break;
                                case RISK_REJECT:
                                    i.or().eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.REJECT);
                                    break;
                                case RISK_LOG:
                                    i.or().eq(SensitiveLog::getCrsDetectStatus, LogEnum.CrsDetectStatus.LOG);
                                    break;
                                case ABRT_REJECT:
                                    break;
                                case ABRT_LOG:
                                    i.or().eq(SensitiveLog::getAbnormalBehaviorDetectStatus,
                                            LogEnum.AbnormalBehaviorDetectStatus.LOG);
                                    break;
                            }

                        }));
            }
        }
        if (StrUtil.isNotEmpty(request.getCity())) {
            query.and(
                    i -> i.or().like(SensitiveLog::getClientCountry, request.getCity())
                            .or().like(SensitiveLog::getClientProvince, request.getCity())
                            .or().like(SensitiveLog::getClientCity, request.getCity())
            );
        }

        query.like(StrUtil.isNotBlank(request.getUri()), SensitiveLog::getUri, request.getUri());
        query.like(StrUtil.isNotBlank(request.getUrl()), SensitiveLog::getUrl, request.getUrl());
        query.like(StrUtil.isNotBlank(request.getContent()), SensitiveLog::getContent, request.getContent());
        query.eq(StrUtil.isNotBlank(request.getExactContent()), SensitiveLog::getContent,
                request.getExactContent());
        query.like(StrUtil.isNotBlank(request.getClientIp()), SensitiveLog::getClientIp, request.getClientIp());
        query.eq(StrUtil.isNotBlank(request.getExactClientIp()), SensitiveLog::getClientIp,
                request.getExactClientIp());
        query.between(request.getStartTime() != null && request.getEndTime() != null, SensitiveLog::getLogTime,
                request.getStartTime(), request.getEndTime());
        query.orderByDesc(SensitiveLog::getLogTime);
        Page<SensitiveLog> page = query.page(new Page<>(request.getPageNum(), request.getPageSize()));
        BaseData baseData = getBaseData();
        List<SensitivePortraitLogResponse> results = page.getRecords().stream().map(r -> {
            SensitivePortraitLogResponse logResponse = new SensitivePortraitLogResponse();
            logResponse.setLogId(r.getLogId());
            logResponse.setLogTime(r.getLogTime());
            logResponse.setApiId(r.getApiId());
            logResponse.setApiName(baseData.getAllApiMap().get(r.getApiId()).getName());
            logResponse.setAppId(r.getAppId());
            logResponse.setAppName(baseData.getAllBizNameAppMap().get(r.getAppId()).getName());
            logResponse.setUri(r.getUri());
            logResponse.setUrl(r.getUrl());
            logResponse.setRequestState(r.getReqState());
            logResponse.setClientIp(r.getClientIp());
            logResponse.setAddr(r.getAddr());
            logResponse.setSensitiveRule(baseData.getAllRuleMap().get(r.getRuleId()).getName());
            logResponse.setSensitiveLevel(baseData.getAllRuleMap().get(r.getRuleId()).getLevel());
            logResponse.setSensitiveContent(r.getContent());
            return logResponse;
        }).collect(Collectors.toList());
        return PageUtils.convertPage(page, results);
    }

    public List<SensitivePortraitResponse.StatContent> queryContent(String apiId,
                                                                    String appId,
                                                                    String clientIp,
                                                                    LocalDateTime startTime,
                                                                    LocalDateTime endTime) {
        List<StatCount> contentCounts = statContent(apiId, appId, clientIp, startTime, endTime);
        return contentCounts.stream()
                .map(slog ->
                        new SensitivePortraitResponse.StatContent(
                                slog.getLabel(),
                                slog.getCount(),
                                getFirstLog(null, apiId, appId, clientIp, startTime, endTime).getLogTime(),
                                getLatestLog(null, apiId, appId, clientIp, startTime, endTime).getLogTime()
                        ))
                .collect(Collectors.toList());
    }

    public List<SensitivePortraitResponse.StatApi> queryApi(String content,
                                                            String appId,
                                                            String clientIp,
                                                            LocalDateTime startTime,
                                                            LocalDateTime endTime,
                                                            BaseData baseData) {
        List<SensitiveApiTriggerCountResponse> contentApis = statApi(content, appId, clientIp, startTime, endTime,
                baseData);
        return contentApis.stream()
                .map(slog ->
                        {
                            SensitivePortraitResponse.StatApi statApi = new SensitivePortraitResponse.StatApi();
                            statApi.setApiId(slog.getApiId());
                            statApi.setApiName(slog.getApiName());
                            statApi.setAppId(slog.getAppId());
                            statApi.setAppName(slog.getAppName());
                            statApi.setSensitiveCount(slog.getCount());
                            statApi.setFirstLogTime(getFirstLog(content, null, appId, clientIp, startTime, endTime).getLogTime());
                            statApi.setLastLogTime(getLatestLog(content, null, appId, clientIp, startTime, endTime).getLogTime());
                            return statApi;
                        }
                )
                .collect(Collectors.toList());
    }

    public List<SensitivePortraitResponse.StatClientIp> queryClientIp(String content,
                                                                      String apiId,
                                                                      String appId,
                                                                      LocalDateTime startTime,
                                                                      LocalDateTime endTime) {
        List<StatCount> contentApis = statClientIp(content, apiId, appId, startTime, endTime);
        return contentApis.stream()
                .map(slog ->
                        {
                            SensitivePortraitResponse.StatClientIp statClientIp =
                                    new SensitivePortraitResponse.StatClientIp();
                            statClientIp.setClientIp(slog.getLabel());
                            statClientIp.setSensitiveCount(slog.getCount());
                            statClientIp.setFirstLogTime(getFirstLog(content, apiId, appId, null, startTime, endTime).getLogTime());
                            SensitiveLog latestLog = getLatestLog(content, apiId, appId, null, startTime, endTime);
                            statClientIp.setLastLogTime(latestLog.getLogTime());
                            statClientIp.setAddr(latestLog.getAddr());
                            // todo isp
                            statClientIp.setIsp(latestLog.getAddr());
                            return statClientIp;
                        }
                )
                .collect(Collectors.toList());
    }

    private List<String> getAppIdWithChildren(List<Application> apps, String appId) {
        Map<String, List<Application>> parentIdMap = apps.stream()
                .collect(Collectors.groupingBy(app -> app.getParentId() == null ? "ROOT" : app.getParentId()));

        List<String> result = new ArrayList<>();
        Deque<String> queue = new ArrayDeque<>();
        queue.add(appId);

        while (!queue.isEmpty()) {
            String current = queue.poll();
            result.add(current); // 直接加入结果
            List<Application> children = parentIdMap.getOrDefault(current, Collections.emptyList());
            for (Application child : children) {
                queue.add(child.getApplicationId());
            }
        }

        return result;
    }

    /**
     * 麻了
     */
    public Object statLeak(QuerySensitivePortraitLeakRequest query) {
        LocalDateTime startTime = query.getStartTime();
        LocalDateTime endTime = query.getEndTime();
        BaseData baseData = getBaseData();

        AtomicInteger index = new AtomicInteger(1);
        switch (query.getType()) {
            case "content": //  api - ip
                String content_content = query.getKeyword();
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("content", content_content)
                        .set("apis", queryApi(content_content, null, null, startTime, endTime, baseData).stream()
                                .map(statApi -> {
                                    String content_apiId = statApi.getApiId();
                                    return JSONUtil.parseObj(statApi)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime", statApi.formatFirstTime())
                                            .set("lastLogTime", statApi.formatLastTime())
                                            .set("clientIps",
                                                    queryClientIp(content_content, content_apiId, null,
                                                            startTime, endTime).stream()
                                                            .map(ele -> JSONUtil.parseObj(ele)
                                                                    .set("firstLogTime", ele.formatFirstTime())
                                                                    .set("lastLogTime", ele.formatLastTime())
                                                                    .set("id", index.getAndIncrement()))
                                                            .collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
            case "api": //  content - ip
                String api_apiId = query.getKeyword();
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("apiId", api_apiId)
                        .set("contents",
                                queryContent(api_apiId, null, null, startTime, endTime).stream()
                                        .map(statContent -> {
                                            String api_content = statContent.getKeyword();
                                            return JSONUtil.parseObj(statContent)
                                                    .set("id", index.getAndIncrement())
                                                    .set("firstLogTime", statContent.formatFirstTime())
                                                    .set("lastLogTime", statContent.formatLastTime())
                                                    .set("clientIps", queryClientIp(api_content, api_apiId, null,
                                                            startTime, endTime).stream()
                                                            .map(ele -> JSONUtil.parseObj(ele)
                                                                    .set("firstLogTime", ele.formatFirstTime())
                                                                    .set("lastLogTime", ele.formatLastTime())
                                                                    .set("id", index.getAndIncrement()))
                                                            .collect(Collectors.toList()));
                                        })
                                        .collect(Collectors.toList()));
            case "app": //  content - api - ip
                String app_appId = query.getKeyword();
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("appId", app_appId)
                        .set("contents", queryContent(null, app_appId, null, startTime, endTime).stream()
                                .map(statContent -> {
                                    String app_content = statContent.getKeyword();
                                    return JSONUtil.parseObj(statContent)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime", statContent.formatFirstTime())
                                            .set("lastLogTime", statContent.formatLastTime())
                                            .set("apis",
                                                    queryApi(app_content, app_appId, null,
                                                            startTime, endTime, baseData).stream()
                                                            .map(statApi -> {
                                                                String app_apiId = statApi.getApiId();
                                                                return JSONUtil.parseObj(statApi)
                                                                        .set("id", index.getAndIncrement())
                                                                        .set("firstLogTime", statApi.formatFirstTime())
                                                                        .set("lastLogTime", statApi.formatLastTime())
                                                                        .set("clientIps", queryClientIp(app_content,
                                                                                app_apiId,
                                                                                app_appId,
                                                                                startTime, endTime).stream()
                                                                                .map(ele -> JSONUtil.parseObj(ele)
                                                                                        .set("id",
                                                                                                index.getAndIncrement())
                                                                                        .set("firstLogTime",
                                                                                                ele.formatFirstTime())
                                                                                        .set("lastLogTime",
                                                                                                ele.formatLastTime())
                                                                                )
                                                                                .collect(Collectors.toList()));
                                                            }).collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
            case "clientIp": //  content - api
                String clientIp_clientIp = query.getKeyword();
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("clientIp", clientIp_clientIp)
                        .set("contents", queryContent(null, null, clientIp_clientIp, startTime, endTime).stream()
                                .map(statContent -> {
                                    String clientIp_content = statContent.getKeyword();
                                    return JSONUtil.parseObj(statContent)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime", statContent.formatFirstTime())
                                            .set("lastLogTime", statContent.formatLastTime())
                                            .set("apis",
                                                    queryApi(clientIp_content, null, clientIp_clientIp,
                                                            startTime, endTime, baseData).stream()
                                                            .map(statApi -> JSONUtil.parseObj(statApi)
                                                                    .set("id", index.getAndIncrement())
                                                                    .set("firstLogTime",
                                                                            statApi.formatFirstTime())
                                                                    .set("lastLogTime",
                                                                            statApi.formatLastTime())
                                                            )
                                                            .collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
        }
        return null;
    }


    private SensitiveLog getLatestLog(String content,
                                      String apiId,
                                      String appId,
                                      String clientIp,
                                      LocalDateTime startTime,
                                      LocalDateTime endTime) {
        Optional<SensitiveLog> sensitiveLog = sensitiveLogService
                .lambdaQuery()
                .eq(StrUtil.isNotBlank(content), SensitiveLog::getContent, content)
                .eq(StrUtil.isNotBlank(apiId), SensitiveLog::getApiId, apiId)
                .eq(StrUtil.isNotBlank(appId), SensitiveLog::getAppId, appId)
                .eq(StrUtil.isNotBlank(clientIp), SensitiveLog::getClientIp, clientIp)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByDesc(SensitiveLog::getLogTime)
                .last("limit 1")
                .oneOpt();
        if (sensitiveLog.isPresent()) {
            return sensitiveLog.get();
        } else {
            log.error("不存在的涉敏日志:{},{},{},{},{},{}", content, apiId, appId, clientIp, startTime, endTime);
            throw new BusinessException("不存在的涉敏日志");
        }
    }

    private SensitiveLog getFirstLog(String content,
                                     String apiId,
                                     String appId,
                                     String clientIp,
                                     LocalDateTime startTime,
                                     LocalDateTime endTime) {
        Optional<SensitiveLog> sensitiveLog = sensitiveLogService
                .lambdaQuery()
                .eq(StrUtil.isNotBlank(content), SensitiveLog::getContent, content)
                .eq(StrUtil.isNotBlank(apiId), SensitiveLog::getApiId, apiId)
                .eq(StrUtil.isNotBlank(appId), SensitiveLog::getAppId, appId)
                .eq(StrUtil.isNotBlank(clientIp), SensitiveLog::getClientIp, clientIp)
                .between(SensitiveLog::getLogTime, startTime, endTime)
                .orderByAsc(SensitiveLog::getLogTime)
                .last("limit 1")
                .oneOpt();
        if (sensitiveLog.isPresent()) {
            return sensitiveLog.get();
        } else {
            log.error("不存在的涉敏日志:{},{},{},{},{},{}", content, apiId, appId, clientIp, startTime, endTime);
            throw new BusinessException("不存在的涉敏日志");
        }
    }

    private BaseData getBaseData() {
        BaseData baseData = new BaseData();
        baseData.setAllRules(sensitiveRuleService.list());
        baseData.setAllRuleIds(baseData.getAllRules().stream().map(SensitiveRule::getId).collect(Collectors.toList()));
        baseData.setAllRuleMap(baseData.getAllRules().stream().collect(Collectors.toMap(SensitiveRule::getId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllApps(applicationService.list());
        baseData.setAllAppIds(baseData.getAllApps().stream().map(Application::getApplicationId).collect(Collectors.toList()));
        baseData.setAllAppMap(baseData.getAllApps().stream().collect(Collectors.toMap(Application::getApplicationId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllBizNameApps(applicationBusinessService.listWithBizName());
        baseData.setAllBizNameAppMap(baseData.getAllBizNameApps().stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllApis(apiInfoService.getBaseMapper().query(new QueryApiRequest()));
        baseData.setAllApiIds(baseData.getAllApis().stream().map(ApiQueryResponse::getId).collect(Collectors.toList()));
        baseData.setAllApiMap(baseData.getAllApis().stream().collect(Collectors.toMap(ApiQueryResponse::getId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllApiMerges(apiMergeService.list());
        baseData.setAllApiMergeMap(baseData.getAllApiMerges().stream().collect(Collectors.toMap(ApiMerge::getId,
                Function.identity(), (k1, k2) -> k1)));
        return baseData;
    }

    @Data
    public static class BaseData {
        private List<SensitiveRule> allRules;
        private Map<String, SensitiveRule> allRuleMap;
        private List<String> allRuleIds;

        private List<Application> allApps;
        private Map<String, Application> allAppMap;
        private List<ApplicationResponse> allBizNameApps;
        private Map<String, ApplicationResponse> allBizNameAppMap;
        private List<String> allAppIds;

        private List<ApiQueryResponse> allApis;
        private Map<String, ApiQueryResponse> allApiMap;
        private List<String> allApiIds;

        private List<ApiMerge> allApiMerges;
        private Map<String, ApiMerge> allApiMergeMap;

        public boolean isEmptyData() {
            return CollUtil.isEmpty(allRules) || CollUtil.isEmpty(allApps) || CollUtil.isEmpty(allApis);
        }

        public boolean containsTarget(String type, String target) {
            if (type.equals("app")) {
                return allAppIds.contains(target);
            } else if (type.equals("api")) {
                return allApiIds.contains(target);
            }
            return true;
        }

        public List<String> getMergeApiId(String apiId) {
            List<String> apiIds;
            ApiMerge apiMerge = allApiMergeMap.get(apiId);
            if (apiMerge == null) {
                apiIds = Collections.singletonList(apiId);
            } else {
                apiIds = apiMerge.getApi();
            }
            return apiIds;
        }
    }
}
