package com.telecom.apigateway.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.Platform;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.telecom.apigateway.model.entity.UrlInfo;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.server.PathContainer;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-09-10
 */
public class ApiUrlUtils {

    public static String getHost(String url) {
        // 正则表达式，匹配IP地址或者域名
        String regex = "https?://([^:/]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        return matcher.find() ? matcher.group(1) : null;
    }

    public static Integer getPort(String url) {
        // 正则表达式，匹配IP地址或域名和端口号（如果有）
        String regex = "https?://([^:/]+)(?::(\\d+))?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        return matcher.find() ? Integer.parseInt(matcher.group(2)) : null;
    }

    public static String getUri(String url) {
        // 正则表达式，只匹配URI部分
        String regex = "https?://[^/]+(/.*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        return matcher.find() ? matcher.group(1) : null;
    }

    public static Pair<String, Integer> getIpPort(String urlStr) throws MalformedURLException {
        int port = 80;
        URL url = new URL(urlStr);
        if (url.getPort() == -1) {
            if (url.getProtocol().equals("https")) {
                port = 443;
            }
        } else {
            port = url.getPort();
        }
        return Pair.of(url.getHost(), port);
    }

    public static URL getUrl(String urlStr) {
        try {
            return new URL(urlStr);
        } catch (MalformedURLException e) {
            return null;
        }
    }

    public static UrlInfo getUrlInfo(String urlStr) throws MalformedURLException {
        int port = 80;
        URL url = new URL(urlStr);
        if (url.getPort() == -1) {
            if (url.getProtocol().equals("https")) {
                port = 443;
            }
        } else {
            port = url.getPort();
        }
        return UrlInfo.builder()
                .ip(url.getHost())
                .port(port)
                .uri(url.getPath())
                .build();
    }


    public static String getDeviceName(String userAgent) {
        UserAgent ua = UserAgentUtil.parse(userAgent);
        if (ua == null) {
            return "未知";
        }
        Platform platform = ua.getPlatform();
        if (platform == null) {
            return "未知";
        }
        String name = platform.getName();
        if (StrUtil.isBlank(name)) {
            return "未知";
        }
        return name;
    }

    public static String getSource(String referer) {
        if (StrUtil.isBlank(referer)) {
            return "未知";
        }
        // 正则表达式匹配域名或者 IP
        String regex = "https?://([^:/]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(referer);
        if (matcher.find()) {
            // 获取匹配的域名或IP部分
            return matcher.group(1);
        } else {
            return "未知";
        }
    }

    /**
     * 判断 pattern1 是否匹配 pattern2 如 "/user/{id}" 匹配 "/user/123"）
     *
     * @param pattern1 路由模式，支持形如 /user/{id}, /user/123
     * @param pattern2 路由模式，支持形如 /user/{id}, /user/123
     * @return 匹配成功返回 true，否则 false
     */
    public static boolean isMatch(String pattern1, String pattern2) {
        String[] patParts = pattern1.split("/");
        String[] pathParts = pattern2.split("/");

        if (patParts.length != pathParts.length) {
            return false;
        }

        for (int i = 0; i < patParts.length; i++) {
            if (patParts[i].startsWith("{") && patParts[i].endsWith("}")) {
                // 路径参数占位符，跳过
                continue;
            }
            if (pathParts[i].startsWith("{") && pathParts[i].endsWith("}")) {
                // 路径参数占位符，跳过
                continue;
            }
            if (!patParts[i].equals(pathParts[i])) {
                return false;
            }
        }
        return true;
    }


    private static final PathPatternParser parser = new PathPatternParser();

    public static void main(String[] args) {
        List<String> patterns = Arrays.asList(
                "/api/{id2}",
                "/api/{id1}",
                "/api/{id:[0-9]+}",
                "/api/123",
                "/api/{name:[a-z]+}",
                "/api/*/detail"
        );

        String requestUri = "/api/id3";

        try {
            String match = findBestMatch(patterns, requestUri);
            System.out.println("✅ Best Match: " + match);
        } catch (IllegalStateException e) {
            System.err.println("❌ " + e.getMessage());
        }
    }

    public static String findBestMatch(List<String> patternStrings, String uri) {
        PathContainer path = PathContainer.parsePath(uri);

        List<MatchEntry> matches = patternStrings.stream()
                .map(pattern -> new MatchEntry(pattern, parser.parse(pattern)))
                .filter(entry -> entry.pattern.matches(path))
                .collect(Collectors.toList());

        if (matches.isEmpty()) return null;

        // 使用 Spring 内部定义 Comparable
        matches.sort(Comparator.comparing(entry -> entry.pattern));

        // 拿到第一条匹配项
        MatchEntry best = matches.get(0);

        // 检查是否有冲突（多个优先级相同的匹配）
        List<MatchEntry> conflicts = matches.stream()
                .filter(entry -> entry.pattern.compareTo(best.pattern) == 0)
                .collect(Collectors.toList());

        if (conflicts.size() > 1) {
            String conflictPatterns = conflicts.stream()
                    .map(entry -> entry.original)
                    .collect(Collectors.joining(", "));
            throw new IllegalStateException("存在路径优先级冲突: " + conflictPatterns);
        }

        return best.original;
    }

    private static class MatchEntry {
        final String original;
        final PathPattern pattern;

        MatchEntry(String original, PathPattern pattern) {
            this.original = original;
            this.pattern = pattern;
        }
    }
}
