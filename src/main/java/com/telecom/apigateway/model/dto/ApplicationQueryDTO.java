package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ApplicationQueryDTO {
    private Integer pageSize;
    private Integer page;
    private String name;
    private String url;
    private String owner;
    private ApplicationSourceEnum source;

    public ApplicationQueryDTO(Integer pageSize, Integer page) {
        this.page = page;
        this.pageSize = pageSize;
    }
}
