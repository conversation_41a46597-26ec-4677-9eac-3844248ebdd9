package com.telecom.apigateway.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * URL端点配置类
 * 用于存储host、port、uri的组合配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UrlEndpoint implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "主机地址")
    @NotBlank(message = "主机地址不能为空")
    @JsonProperty("host")
    private String host;
    
    @Schema(description = "端口号")
    @Pattern(regexp = "^[1-9]\\d{0,4}$", message = "端口号格式不正确")
    @JsonProperty("port")
    private String port;
    
    @Schema(description = "URI路径")
    @JsonProperty("uri")
    private String uri;
    
    @Schema(description = "协议类型")
    @JsonProperty("protocol")
    private String protocol = "HTTP";
    
    /**
     * 获取完整的URL
     * @return 完整URL字符串
     */
    public String getFullUrl() {
        StringBuilder url = new StringBuilder();
        
        // 添加协议
        if (protocol != null && !protocol.isEmpty()) {
            url.append(protocol.toLowerCase()).append("://");
        }
        
        // 添加host
        url.append(host);
        
        // 添加port（如果不是默认端口）
        if (port != null && !port.isEmpty()) {
            if (!"80".equals(port) || !"443".equals(port)) {
                url.append(":").append(port);
            }
        }
        
        // 添加uri
        if (uri != null && !uri.isEmpty()) {
            if (!uri.startsWith("/")) {
                url.append("/");
            }
            url.append(uri);
        }
        
        return url.toString();
    }
    
    /**
     * 获取host:port格式的地址
     * @return host:port字符串
     */
    public String getHostPort() {
        if (port != null && !port.isEmpty()) {
            return host + ":" + port;
        }
        return host;
    }
}
