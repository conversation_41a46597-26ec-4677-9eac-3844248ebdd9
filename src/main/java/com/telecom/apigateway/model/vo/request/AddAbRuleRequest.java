package com.telecom.apigateway.model.vo.request;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
public class AddAbRuleRequest implements Serializable {
    @NotNull(message = "限制类型不能为空")
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;

    @NotBlank(message = "名称不能为空")
    @Size(max = 20, message = "名称长度不能超过20个字符")
    private String name;

    @NotNull(message = "资产类型不能为空")
    private AbnormalBehaviorRuleEnum.AssetType assetType;

    @NotNull(message = "资产内容不能为空")
    @NotEmpty(message = "资产内容不能为空")
    private String[] assetId;

    @Schema(description = "匹配方式")
    private AbnormalBehaviorRuleEnum.Operation abnormalOperation;

    @Schema(description = "持续时间s")
    @Min(value = 10, message = "持续时间不能超过10~600")
    @Max(value = 600, message = "持续时间不能超过10~600")
    private Long abnormalDuration;

    @Schema(description = "异常阈值")
    @NotNull(message = "异常阈值不能为空")
    @Min(value = 1, message = "异常阈值不能小于1")
    private Long abnormalThreshold;

    @NotNull(message = "匹配方式不能为空")
    @Size(max = 4, message = "匹配方式超过限制")
    private List<AbnormalBehaviorRuleCondition> condition;

    @NotNull(message = "限制策略不能为空")
    private AbnormalBehaviorRuleEnum.Policy policy;

    @Min(value = 1, message = "封禁时间不能少于 1 分钟")
    @Max(value = Integer.MAX_VALUE, message = "封禁时间不能超过限制")
    private Long policyDuration;

    @Schema(description = "优先级")
    @NotNull(message = "不能为空")
    @Max(value = 100, message = "优先级不能超过100")
    @Min(value = 1, message = "优先级不能小于1")
    private Integer priority;

    public void format() {
        name = name.trim();
        for (AbnormalBehaviorRuleCondition c : condition) {
            c.format();
        }
    }

    public void valid() {
        BusinessException businessException = checkReturnException();
        if (businessException != null) {
            throw businessException;
        }
    }

    public BusinessException checkReturnException() {
        String errorMsg = getErrorMsg();
        if (errorMsg != null) {
            return new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, errorMsg);
        }
        return null;
    }

    private String getErrorMsg() {
        List<AbnormalBehaviorRuleEnum.AbnormalType> onlyBannedPolicy =
                Arrays.asList(AbnormalBehaviorRuleEnum.AbnormalType.ATTACK,
                        AbnormalBehaviorRuleEnum.AbnormalType.ERROR,
                        AbnormalBehaviorRuleEnum.AbnormalType.SENSITIVE);
        // 校验: 异常类型 -> 限制时间
        if (abnormalType == AbnormalBehaviorRuleEnum.AbnormalType.VISIT &&
                policy == AbnormalBehaviorRuleEnum.Policy.BANNED) {
            if (policyDuration == null || policyDuration <= 0) {
                return "限制时间错误";
            }
        } else if (onlyBannedPolicy.contains(abnormalType)) {
            if (policy != AbnormalBehaviorRuleEnum.Policy.BANNED && policy != AbnormalBehaviorRuleEnum.Policy.WARNING) {
                return "该限流类型只能选择封禁或告警";
            }
        }

        // 校验: 用户规则 -> 匹配条件
        for (AbnormalBehaviorRuleCondition c : condition) {
            if (c.getTarget() == null) {
                return "匹配对象值不正确";
            }
            if (!abnormalType.getTargets().contains(c.getTarget())) {
                return "限制类型匹配对象不正确";
            }

            if (c.getOperation() == null) {
                return "匹配方式不正确";
            }
            if (StrUtil.isBlank(c.getValue())) {
                return "匹配内容不能为空";
            }
            if (!CollUtil.contains(c.getTarget().getOperations(), c.getOperation())) {
                return "匹配对象对应匹配方式不正确";
            }
            if (c.getTarget() == AbnormalBehaviorRuleEnum.Target.header) {
                if (StrUtil.isBlank(c.getHeaderName())) {
                    return "匹配头不能为空";
                }
            }
        }
        return null;
    }

    public AbnormalBehaviorRule toRule() {
        LocalDateTime now = LocalDateTime.now();
        return AbnormalBehaviorRule.builder()
                .name(name)
                .abnormalType(abnormalType)
                .assetType(assetType)
                .assetIds(Arrays.asList(assetId))
                .abnormalOperation(abnormalOperation)
                .abnormalThreshold(abnormalThreshold)
                .abnormalDuration(abnormalDuration)
                .condition(condition)
                .source(AbnormalBehaviorRuleEnum.Source.CUSTOM)
                .priority(priority)
                .policy(policy)
                .policyDuration(policyDuration)
                .enable(false)
                .isDeleted(false)
                .createTime(now)
                .createUser(StpUtil.getLoginIdAsString())
                .updateTime(now)
                .updateUser(StpUtil.getLoginIdAsString())
                .build();
    }
}
