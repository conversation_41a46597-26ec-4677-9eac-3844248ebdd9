package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.config.StringListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@TableName(autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AbnormalBehaviorRule implements Serializable {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 规则名
     */
    @Schema(description = "规则名称")
    private String name;

    /**
     * 类型
     */
    @Schema(description = "异常类型")
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;
    /**
     * 资产类型
     */
    @Schema(description = "资产类型")
    private AbnormalBehaviorRuleEnum.AssetType assetType;
    /**
     * 资产id
     */
    @Schema(description = "资产id")
    @TableField(value = "asset_id", typeHandler = StringListTypeHandler.class)
    private List<String> assetIds;
    /**
     * 匹配方式
     */
    @Schema(description = "匹配方式")
    private AbnormalBehaviorRuleEnum.Operation abnormalOperation;
    /**
     * 持续时间(s)
     */
    @Schema(description = "持续时间")
    private Long abnormalDuration;
    /**
     * 阈值
     */
    @Schema(description = "异常阈值")
    private Long abnormalThreshold;

    /**
     * 规则条件
     */
    @Schema(description = "匹配条件")
    @TableField(typeHandler = JsonbListTypeHandler.class)
    private List<AbnormalBehaviorRuleCondition> condition;

    /**
     * 来源(SYSTEM,MANUAL)
     */
    @Schema(description = "规则来源")
    private AbnormalBehaviorRuleEnum.Source source;
    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 限制策略
     */
    @Schema(description = "限制策略")
    private AbnormalBehaviorRuleEnum.Policy policy;

    /**
     * 策略持续时间(分钟)
     */
    @Schema(description = "策略持续时间")
    private Long policyDuration;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @TableField("is_enable")
    private Boolean enable;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     */
    private String updateUser;

    public String getPolicyDetail() {
        if (policy == AbnormalBehaviorRuleEnum.Policy.BANNED) {
            return getPolicyDesc() + policyDuration + "分钟";
        } else {
            return getPolicyDesc();
        }
    }

    public String getPolicyDesc() {
        switch (policy) {
            case WARNING:
                return "警告";
            case BLOCK:
                return "拦截";
            case BANNED:
                return "封禁";
        }
        return "未知";
    }
}
