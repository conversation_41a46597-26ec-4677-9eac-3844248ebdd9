package com.telecom.apigateway.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.dto.*;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.ApplicationBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@RestController
@RequestMapping("/api/application")
@Tag(name = "应用资源管理", description = "用于管理应用资源的相关操作")
@AllArgsConstructor
@CheckApiPermission("APP_LIST")
public class ApplicationController {
    private final ApplicationBusinessService applicationBusinessService;

    @PostMapping
    @Operation(summary = "添加应用")
    @CheckApiPermission("APP_LIST")
    public Result<String> addApplication(@RequestBody @Valid AddApplicationRequest request) {
        String username = StpUtil.getLoginIdAsString();
        String applicationId = applicationBusinessService.addApplication(username, request);
        return Result.success(applicationId);
    }

    @PutMapping
    @Operation(summary = "修改应用")
    public Result<String> updateApplication(@RequestBody @Valid UpdateApplicationRequest request) {
        Application application = applicationBusinessService.updateApplication(StpUtil.getLoginIdAsString(), request);
        String applicationId = application.getApplicationId();
        return Result.success(applicationId);
    }

    @DeleteMapping("/{applicationId}")
    @Operation(summary = "删除应用")
    public Result<Void> deleteApplication(@PathVariable("applicationId") String applicationId) {
        applicationBusinessService.deleteApplication(StpUtil.getLoginIdAsString(), applicationId);
        return Result.success(null);
    }

    @GetMapping("/query")
    @Operation(summary = "条件查询")
    public Result<Page<ApplicationResponse>> list(
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "pageNum", required = false) Integer page,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "owner", required = false) String owner,
            @RequestParam(value = "url", required = false) String url
    ) {
        ApplicationQueryDTO applicationQueryDTO = new ApplicationQueryDTO(pageSize, page, name, url,owner, ApplicationSourceEnum.MANUAL_ADD);
        Page<ApplicationResponse> result = applicationBusinessService.query(StpUtil.getLoginIdAsString(), applicationQueryDTO);
        return Result.success(result);
    }

    @GetMapping("/option")
    @Operation(summary = "获取下拉框查询选项")
    @CheckApiPermission(exclude = true)
    public Result<List<ApplicationOptionResponse>> option() {
        List<ApplicationOptionResponse> result = applicationBusinessService.getOption();
        return Result.success(result);
    }

    @GetMapping("/overview")
    @Operation(summary = "获得概览数量")
    public Result<ApplicationOverviewResponse> overview() {
        ApplicationOverviewResponse result = applicationBusinessService.getOverview();
        return Result.success(result);
    }

    @GetMapping("/{applicationId}")
    @Operation(summary = "获取应用详情")
    public Result<ApplicationDetailResponse> getDetail(@PathVariable("applicationId") String applicationId,
                                                       @RequestParam(value = "isBaseApplication", required = false, defaultValue = "false") Boolean isBaseApplication) {
        ApplicationDetailResponse result = applicationBusinessService.getDetail(applicationId, isBaseApplication);
        return Result.success(result);
    }

    @GetMapping("/{applicationId}/risks")
    @Operation(summary = "获取应用近7天风险概览")
    public Result<ApplicationRiskResponse> getRisks(@PathVariable("applicationId") String applicationId,
                                                    @RequestParam(value = "isBaseApplication", required = false, defaultValue = "false") Boolean isBaseApplication) {
        ApplicationRiskResponse result = applicationBusinessService.getRisksByApplicationId(applicationId, isBaseApplication);
        return Result.success(result);
    }

    @GetMapping("/{applicationId}/stat")
    @Operation(summary = "获取近7天访问概览")
    public Result<ApiStatResponse> getVisitors(@PathVariable("applicationId") String applicationId,
                                               @RequestParam(value = "isBaseApplication", required = false, defaultValue = "false") Boolean isBaseApplication) {
        ApiStatResponse result = applicationBusinessService.getVisitorsByApplicationId(applicationId, isBaseApplication);
        return Result.success(result);
    }

    @GetMapping("/{applicationId}/attack/stat")
    public Result<ApiAttackStatResponse> statAttack(@PathVariable String applicationId,
                                                    @RequestParam(value = "isBaseApplication", required = false, defaultValue = "false") Boolean isBaseApplication) {
        ApiAttackStatResponse stat = applicationBusinessService.getStatAttack(applicationId, isBaseApplication);
        return Result.success(stat);
    }

    @PostMapping("/batch")
    @Operation(summary = "批量添加应用")
    public Result<Void> batchAddApplication(@RequestBody @Valid BatchAddApplicationRequest request) {
        String username = StpUtil.getLoginIdAsString();
        for (AddApplicationRequest application : request.getApplications()) {
            applicationBusinessService.addApplication(username, application);
        }
        return Result.success(null);
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "删除应用")
    public Result<Void> batchDeleteApplication(@NotEmpty @RequestBody List<String> applicationIds) {
        for (String applicationId : applicationIds) {
            applicationBusinessService.deleteApplication(StpUtil.getLoginIdAsString(), applicationId);
        }
        return Result.success(null);
    }

    @GetMapping("/risk/summary")
    public Result<ApplicationRiskSummaryResponse> riskSummary() {
        ApplicationRiskSummaryResponse summary = applicationBusinessService.riskSummary();
        return Result.success(summary);
    }

    @GetMapping("/risk/screen/summary")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<ApplicationRiskSummaryDTO> riskSummaryByApplicationId(@ModelAttribute AttackStatScreenRequest request) {
        ApplicationRiskSummaryDTO summary = applicationBusinessService.getRiskSummaryByApplicationId(request);
        return Result.success(summary);
    }

    @GetMapping("/attack/screen/stat")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<ApplicationAttackStatDTO> attackStatByApplicationId(@ModelAttribute AttackStatScreenRequest request) {
        ApplicationAttackStatDTO stat = applicationBusinessService.getAttackStatByApplicationId(request);
        return Result.success(stat);
    }

    @GetMapping("/attack/screen/map")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<ApplicationAttackMapDTO> attackMapByApplicationId(@ModelAttribute AttackStatScreenRequest request) {
        ApplicationAttackMapDTO stat = applicationBusinessService.getAttackMapByApplicationId(request);
        return Result.success(stat);
    }

    @GetMapping("/permission/options")
    @CheckApiPermission(value = {"ACCESS_CONTROL"})
    public Result<List<ApplicationPermissionResponse>> getApplicationPermissionOptions() {
        List<ApplicationPermissionResponse> stat = applicationBusinessService.getApplicationPermissionOptions();
        return Result.success(stat);
    }
}
