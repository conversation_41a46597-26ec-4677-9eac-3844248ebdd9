<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ApiInfoMapper">
    <resultMap id="BaseResultMap" type="com.telecom.apigateway.model.vo.response.ApiQueryResponse">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name"/>
        <result column="app_id" property="appId"/>
        <result column="discover_time" property="discoverTime"/>
        <result column="http_methods" property="httpMethods" jdbcType="ARRAY"
                typeHandler="com.telecom.apigateway.config.mybatisplus.StringListTypeHandler"/>
        <result column="is_active" property="isActive"/>
        <result column="is_online" property="isOnline"/>
        <result column="sensitive_level" property="sensitiveLevel"/>
        <result column="remark" property="remark"/>
        <result column="source" property="source"/>
        <result column="uri" property="uri"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="online_time" property="onlineTime"/>
        <result column="app_name" property="appName"/>
        <result column="hosts" property="hosts" jdbcType="ARRAY"
                typeHandler="com.telecom.apigateway.config.mybatisplus.StringListTypeHandler"/>
        <result column="port" property="port"/>
        <result column="tagIdsStr" property="tagIdsStr"/>
        <result column="is_encrypt" property="isEncryptApi"/>
        <result column="merge_id" property="mergeId"/>
        <result column="main_application_id" property="mainApplicationId"/>
        <result column="is_merged" property="merged"/>
    </resultMap>

    <select id="queryExportDataByIds" resultType="java.util.LinkedHashMap">
        select api.name                       as "名称",
               http_methods                    as "请求方法",
               case
                   when is_sensitive = true then '是'
                   else '否'
                   end                        as "是否敏感",
               uri                            as "路径",
               case
                   when is_active = true then '是'
                   else '否'
                   end                        as "是否活跃",
               case
                   when is_online = true then '上线'
                   else '下线'
                   end                        as "状态",
               string_agg(tags.tag_name, ';') AS "标签",
               case
                   when source = 0 then '自动'
                   else '手动'
                   end                        as "来源",
               remark                         as "备注",
               discover_time                  as "发现时间"
        from api
                 left join api_tag_relations rel on api.id = rel.api_id
                 left join tags on rel.tag_id = tags.tag_id
        where api.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by api.id, api.name, api.http_methods, api.is_sensitive, api.uri,
                 api.is_active,
                 api.is_online, api.source, api.remark, api.discover_time;
    </select>

    <select id="query" resultMap="BaseResultMap">
        SELECT api.id,
                api.merge_id,
                api.name,
                api.app_id,
                api.main_application_id,
                api.http_methods,
                api.hosts,
                api.port,
                api.uri,
                api.is_active,
                api.is_online,
                api.is_merged,
                api.source,
                api.remark,
                api.discover_time,
                api.create_time,
                api.update_time,
                api.online_time,
                api.is_encrypt,
                coalesce(sensitive_rules, '') AS tagIdsStr,
                coalesce(max_level, 0)        as sensitive_level,
                am.name as mergeName
        from (select * from  api
                left join (select api_id,
                string_agg(sensitive_rule_id, ',') as sensitive_rules,
                max(level)                         as max_level
                from sensitive_api sapi
                left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
                where sapi.is_deleted = false
                and sapi.is_dealt = false
                and rule.is_deleted = false
                group by api_id) rel on api.id = rel.api_id) api
        left join api_merge am on api.id = am.id
        <where>
            api.is_deleted = false
            <if test="query.name != null and query.name != ''">
                and api.name like concat('%', #{query.name}::text, '%')
            </if>
            <if test="query.appIds != null and query.appIds.size > 0">
                and api.app_id in
                <foreach collection="query.appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="query.httpMethod != null and query.httpMethod != ''">
                and #{query.httpMethod} = any(api.http_methods)
            </if>
            <if test="query.uri != null and query.uri != ''">
                and api.uri like concat('%', #{query.uri}::text, '%')
            </if>
            <if test="query.host != null and query.host != ''">
                and #{query.host} = any(api.hosts)
            </if>
            <if test="query.port != null">
                and api.port = #{query.port}
            </if>
            <if test="query.sensitiveLevels != null and query.sensitiveLevels.length > 0">
                and coalesce(max_level, 0)  in
                <foreach collection="query.sensitiveLevels" item="sensitiveLevel" open="(" separator="," close=")">
                    #{sensitiveLevel}
                </foreach>
            </if>
            <if test="query.isOnline != null">
                and api.is_online = #{query.isOnline}
            </if>
            <if test="query.rangeType != null and query.rangeType != '' and query.start != null and query.end != null">
                and api.${query.rangeType} between #{query.start} and #{query.end}
            </if>
            <if test="query.isEncryptApi != null">
                and api.is_encrypt = #{query.isEncryptApi}
            </if>
            <if test="query.tagIds != null and query.tagIds.size > 0">
                and exists (select 1 from sensitive_api rel1
                                where rel1.api_id = api.id
                                and rel1.is_deleted = false
                                and rel1.is_dealt = false
                                and rel1.sensitive_rule_id in
                                <foreach collection="query.tagIds" item="tagId" open="(" separator="," close=")">
                                    #{tagId}
                                </foreach>
                            )
            </if>
            <if test="query.aliveApiIds != null and query.aliveApiIds.size > 0">
                and api.id
                <if test="query.isActive != null and query.isActive == false">
                    not
                </if>
                in
                <foreach collection="query.aliveApiIds" item="apiId" open="(" separator="," close=")">
                    #{apiId}
                </foreach>
            </if>
            <if test="query.source != null">
                and api.source = #{query.source}
            </if>
        </where>
        ORDER BY api.create_time DESC
    </select>

    <select id="queryByIds" resultMap="BaseResultMap">
        SELECT api.id,
                api.merge_id,
                api.name,
                api.app_id,
                api.main_application_id,
                api.http_methods,
                api.hosts,
               api.port,
               api.uri,
               api.is_active,
               api.is_online,
                api.is_merged,
               api.source,
               api.remark,
               api.discover_time,
               api.create_time,
               api.update_time,
               api.online_time,
               api.is_encrypt,
               coalesce(sensitive_rules, '') AS tagIdsStr,
               coalesce(max_level, 0)        as sensitive_level
        from (select *
              from api
                       left join (select api_id,
                                         string_agg(sensitive_rule_id, ',') as sensitive_rules,
                                         max(level)                         as max_level
                                  from sensitive_api sapi
                                           left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
                                  where sapi.is_deleted = false
                                    and sapi.is_dealt = false
                                    and rule.is_deleted = false
                                  group by api_id) rel on api.id = rel.api_id) api
        <where>
            <if test="containDeleted == false">
                and api.is_deleted = false
            </if>
            and api.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        order by api.create_time desc
    </select>

    <select id="queryParentAndChildrenByIds" resultType="com.telecom.apigateway.model.entity.ApiInfoTree">
        with recursive api as (
            select * from api
            union all
            select ai.* from api ai
                                 inner join api at on ai.merge_id = at.id
        )
        select * from api
    </select>

    <!-- 根据应用的JSONB URL端点查找匹配的API -->
    <select id="findApisByApplicationEndpoints" resultType="com.telecom.apigateway.model.entity.ApiInfo">
        SELECT DISTINCT api.*
        FROM api
        INNER JOIN applications app ON app.application_id = #{applicationId}
        CROSS JOIN jsonb_array_elements(app.url_endpoints) AS endpoint
        WHERE api.is_deleted = false
          AND app.url_endpoints IS NOT NULL
          AND endpoint->>'host' = ANY(api.hosts)
          AND api.port = CAST(endpoint->>'port' AS INTEGER)
          AND (
              endpoint->>'uri' IS NULL
              OR endpoint->>'uri' = ''
              OR starts_with(api.uri, endpoint->>'uri')
          )
        ORDER BY api.create_time DESC
    </select>
</mapper>
