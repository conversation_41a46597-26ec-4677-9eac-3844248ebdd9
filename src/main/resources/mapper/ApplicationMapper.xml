<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ApplicationMapper">

    <select id="getWithChildren" resultType="com.telecom.apigateway.model.entity.Application">
        WITH RECURSIVE sub_categories AS (SELECT *
        FROM applications
        WHERE application_id in
        <foreach item="applicationId" collection="applicationIds" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
        UNION ALL
        SELECT c.*
        FROM applications c
        INNER JOIN sub_categories sc ON c.parent_id = sc.application_id)
        SELECT *
        FROM sub_categories
    </select>

    <select id="getRootApplication" resultType="com.telecom.apigateway.model.entity.Application">
        WITH RECURSIVE app_tree AS (
        SELECT *, 1 as level
        FROM applications
        WHERE application_id = #{application_id}

        UNION ALL

        SELECT a.*, t.level + 1
        FROM applications a
        INNER JOIN app_tree t ON t.parent_id = a.application_id
        )
        SELECT DISTINCT ON (level)
        *,
        level
        FROM app_tree
        WHERE parent_id IS NULL
        OR (application_id = #{application_id} AND parent_id IS NULL)
        ORDER BY level DESC
        LIMIT 1;
    </select>
    <select id="listWithChainName" resultType="com.telecom.apigateway.model.entity.Application">
        WITH RECURSIVE app_hierarchy AS (SELECT id,
        application_id,
        create_user_id,
        name,
        host,
        port,
        uri,
        remark,
        create_time,
        update_time,
        is_deleted,
        protocol,
        owner,
        phone,
        email,
        type,
        parent_id,
        area
        FROM applications
        WHERE parent_id IS NULL
        UNION ALL
        SELECT c.id,
        c.application_id,
        c.create_user_id,
        ch.name || '-' || c.name AS name,
        c.host,
        c.port,
        c.uri,
        c.remark,
        c.create_time,
        c.update_time,
        c.is_deleted,
        c.protocol,
        c.owner,
        c.phone,
        c.email,
        c.type,
        c.parent_id,
        c.area
        FROM applications c
        JOIN app_hierarchy ch ON c.parent_id = ch.application_id)
        select *
        from app_hierarchy
        where is_deleted = false
    </select>

    <!-- 分页查询应用列表 -->
    <select id="selectApplicationPage" resultType="com.telecom.apigateway.model.entity.Application">
        SELECT * FROM applications
        WHERE is_deleted = false
        <choose>
            <!-- 如果没有任何搜索条件，则查询 parent_id 为 null 的记录 -->
            <when test="query.name == null and query.owner == null and query.url == null">
                AND parent_id IS NULL
            </when>
            <otherwise>
                <if test="query.name != null and query.name != ''">
                    AND name ILIKE '%' || #{query.name} || '%'
                </if>

                <!-- 所有者查询条件 -->
                <if test="query.owner != null and query.owner != ''">
                    AND owner ILIKE '%' || #{query.owner} || '%'
                </if>

                <!-- URL查询条件 - 支持JSONB数组格式 -->
                <if test="query.url != null and query.url != ''">
                    AND (url_endpoints IS NOT NULL AND EXISTS (
                            SELECT 1 FROM jsonb_array_elements(url_endpoints) AS endpoint
                            WHERE (
                                endpoint->>'host' ||
                                CASE WHEN endpoint->>'port' IS NOT NULL AND endpoint->>'port' != ''
                                     THEN ':' || endpoint->>'port' ELSE '' END ||
                                COALESCE(endpoint->>'uri', '')
                            ) ILIKE '%' || #{query.url} || '%'
                        )
                    )
                </if>
            </otherwise>
        </choose>
        ORDER BY create_time DESC
    </select>

    <!-- 根据host和port查询应用，使用JSONB数组格式 -->
    <select id="getByUrl" resultType="com.telecom.apigateway.model.entity.Application">
        SELECT * FROM applications
        WHERE is_deleted = false
          AND url_endpoints IS NOT NULL
          AND EXISTS (
              SELECT 1 FROM jsonb_array_elements(url_endpoints) AS endpoint
              WHERE endpoint->>'host' = #{host}
                AND endpoint->>'port' = #{port}
          )
    </select>
</mapper>
