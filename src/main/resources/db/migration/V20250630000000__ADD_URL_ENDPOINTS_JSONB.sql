-- 为applications表添加url_endpoints JSONB字段
ALTER TABLE applications 
ADD COLUMN url_endpoints JSONB;

-- 添加列注释
COMMENT ON COLUMN applications.url_endpoints IS 'URL端点配置数组，存储host、port、uri、protocol等信息的JSONB格式';

-- 创建JSONB字段的GIN索引，提高查询性能
CREATE INDEX idx_applications_url_endpoints_gin ON applications USING GIN (url_endpoints);

-- 创建用于查询特定host的表达式索引
CREATE INDEX idx_applications_url_endpoints_host ON applications USING GIN ((url_endpoints -> 'host'));

-- 创建用于查询特定port的表达式索引  
CREATE INDEX idx_applications_url_endpoints_port ON applications USING GIN ((url_endpoints -> 'port'));

-- 迁移现有数据：将现有的host、port、uri字段合并到url_endpoints数组中
UPDATE applications 
SET url_endpoints = jsonb_build_array(
    jsonb_build_object(
        'host', host,
        'port', port,
        'uri', COALESCE(uri, ''),
        'protocol', COALESCE(protocol, 'HTTP')
    )
)
WHERE url_endpoints IS NULL 
  AND host IS NOT NULL 
  AND port IS NOT NULL;

-- 为了保持向后兼容，暂时保留原有字段
-- 后续可以考虑删除 host、port、uri 字段
-- ALTER TABLE applications DROP COLUMN host;
-- ALTER TABLE applications DROP COLUMN port; 
-- ALTER TABLE applications DROP COLUMN uri;
